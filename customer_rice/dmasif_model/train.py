

import torch
import numpy as np
from pathlib import Path
from Bio.PDB import <PERSON><PERSON><PERSON><PERSON><PERSON>
from torch_geometric.data import <PERSON><PERSON>oa<PERSON>, Dataset
from sklearn.model_selection import train_test_split
import os

# Assume dMaSIF modules are accessible
from dMaSIF.model import dMaSIF
from dMaSIF.data import PairData
from dMaSIF.data_iteration import iterate

# --- Configuration Class ---
class TrainConfig:
    def __init__(self):
        # --- Paths ---
        # Directory containing all PDB files for the dMaSIF benchmark
        self.pdb_dir = "/Users/<USER>/Documents/GitHub/dMaSIF/surface_data/raw/01-benchmark_pdbs/"
        # The official list of interacting pairs for training from the dMaSIF paper
        self.pairs_file = "/Users/<USER>/Documents/GitHub/dMaSIF/data/masif_site/lists/training.txt"
        # For this specific dataset, a separate labels file is not used.
        # The script will assume all pairs in training.txt are positive interactions (label=1).
        self.labels_file = "" 
        self.model_save_path = "dmasif_benchmark_trained_model.pth"

        # --- Training Parameters ---
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.epochs = 20
        self.batch_size = 1
        self.learning_rate = 1e-4
        self.test_split_ratio = 0.15

        # --- Model Parameters (should match dMaSIF defaults or your specific needs) ---
        self.use_mesh = False
        self.random_rotation = True
        self.in_channels = 16
        self.orientation_units = 16
        self.post_units = 16
        self.emb_dims = 16
        self.n_layers = 3
        self.radius = 12.0
        self.k = 40
        self.dropout = 0.1
        self.site = False
        self.search = True
        self.atom_dims = 6
        self.resolution = 1.0
        self.sup_sampling = 10
        self.distance = 1.05
        self.no_chem = False
        self.no_geom = False
        self.embedding_layer = "dMaSIF"
        self.seed = 42

ele2num = {"C": 0, "H": 1, "O": 2, "N": 3, "S": 4, "SE": 5}

# --- PDB Preprocessing ---
def preprocess_pdb(pdb_file):
    parser = PDBParser(QUIET=True)
    structure = parser.get_structure("protein", pdb_file)
    atoms = list(structure.get_atoms())
    coords = np.array([atom.get_coord() for atom in atoms], dtype=np.float32)
    types_array = np.zeros((len(atoms), len(ele2num)), dtype=np.float32)
    for i, atom in enumerate(atoms):
        if atom.element in ele2num:
            types_array[i, ele2num[atom.element]] = 1.0
    return {"atom_xyz": torch.from_numpy(coords), "atomtypes": torch.from_numpy(types_array)}

# --- PyTorch Geometric Dataset ---
class ProteinPairDataset(Dataset):
    def __init__(self, pdb_dir, pair_ids, labels):
        super(ProteinPairDataset, self).__init__()
        self.pdb_dir = Path(pdb_dir)
        self.pair_ids = pair_ids
        self.labels = labels
        self.cache = {}

    def __len__(self):
        return len(self.pair_ids)

    def __getitem__(self, idx):
        p1_id, p2_id = self.pair_ids[idx]
        label = self.labels[idx]

        # Load and preprocess PDBs, using a cache for efficiency
        if p1_id in self.cache:
            p1_data = self.cache[p1_id]
        else:
            p1_data = preprocess_pdb(self.pdb_dir / f"{p1_id}.pdb")
            self.cache[p1_id] = p1_data

        if p2_id in self.cache:
            p2_data = self.cache[p2_id]
        else:
            p2_data = preprocess_pdb(self.pdb_dir / f"{p2_id}.pdb")
            self.cache[p2_id] = p2_data

        return PairData(
            p1_atom_xyz=p1_data["atom_xyz"],
            p1_atomtypes=p1_data["atomtypes"],
            p2_atom_xyz=p2_data["atom_xyz"],
            p2_atomtypes=p2_data["atomtypes"],
            y=torch.tensor([label], dtype=torch.float)
        )

# --- Main Training Script ---
if __name__ == "__main__":
    args = TrainConfig()

    # Set seed for reproducibility
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)

    # --- Load Pair and Label Data ---
    with open(args.pairs_file, 'r') as f:
        # Assuming format: PDB1_ID PDB2_ID (optional other columns)
        pair_ids = [line.strip().split()[:2] for line in f.readlines()]
    
    # For dMaSIF's default lists, the label is encoded in the list name or structure
    # Here we assume a simple case where all pairs in the file are positive (interacting)
    # For a mixed file, you would need a separate labels.txt file.
    if args.labels_file and Path(args.labels_file).exists():
         with open(args.labels_file, 'r') as f:
            labels = [int(line.strip()) for line in f.readlines()]
    else:
        print("No labels file found. Assuming all pairs are positive (label=1).")
        labels = [1] * len(pair_ids)

    # --- Split data into training and validation sets ---
    train_pairs, val_pairs, train_labels, val_labels = train_test_split(
        pair_ids, labels, test_size=args.test_split_ratio, random_state=args.seed, stratify=labels
    )

    train_dataset = ProteinPairDataset(args.pdb_dir, train_pairs, train_labels)
    val_dataset = ProteinPairDataset(args.pdb_dir, val_pairs, val_labels)

    batch_vars = ["xyz_p1", "xyz_p2", "atom_coords_p1", "atom_coords_p2"]
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, follow_batch=batch_vars)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, follow_batch=batch_vars)

    print(f"Data loaded: {len(train_dataset)} training pairs, {len(val_dataset)} validation pairs.")

    # --- Initialize Model and Optimizer ---
    model = dMaSIF(args).to(args.device)
    optimizer = torch.optim.Adam(model.parameters(), lr=args.learning_rate)

    print("Starting training...")
    best_val_auc = 0.0

    for epoch in range(args.epochs):
        # --- Training Phase ---
        model.train()
        train_info = iterate(model, train_loader, optimizer, args, test=False)
        avg_train_loss = np.mean(train_info['Loss'])
        avg_train_auc = np.mean(train_info['ROC-AUC'])
        print(f"Epoch {epoch+1}/{args.epochs} | Train Loss: {avg_train_loss:.4f} | Train AUC: {avg_train_auc:.4f}")

        # --- Validation Phase ---
        model.eval()
        with torch.no_grad():
            val_info = iterate(model, val_loader, None, args, test=True)
        
        avg_val_loss = np.mean(val_info['Loss'])
        avg_val_auc = np.mean(val_info['ROC-AUC'])
        print(f"Validation Loss: {avg_val_loss:.4f} | Validation AUC: {avg_val_auc:.4f}")

        # --- Save Best Model ---
        if avg_val_auc > best_val_auc:
            best_val_auc = avg_val_auc
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': avg_train_loss,
            }, args.model_save_path)
            print(f"New best model saved to {args.model_save_path} with AUC: {avg_val_auc:.4f}")

    print("\nTraining finished.")
    print(f"Best validation AUC achieved: {best_val_auc:.4f}")
    print(f"Trained model saved at: {args.model_save_path}")
