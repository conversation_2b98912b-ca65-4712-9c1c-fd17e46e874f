# Gemini Project Configuration

This file provides instructions for the Gemini agent to interact with this project.

## Build & Test Commands

- **Build:** 你需要登录到远端服务器进行运行和测试，首先同步代码到服务器，ip为***********, 账户为ho<PERSON><PERSON><PERSON>,首先需要使用source ~/.bashrc_cu118 命令切换cuda环境，使用 conda 环境为customer_rice_gnn运行，代码需要同步到~/code/customer_rice
- **Test:** 运行环境同 **Build**
- **Linter:** `echo "No linter specified"`

## Project Overview

- **Primary Language:** Python
- **Key Files:**
  - `vqvae_ver2_0717_2.py`: Contains the core VQ-VAE model for protein structure encoding and a training script.
  - `VQVAE_VER1.py`: A script for protein-protein interaction (PPI) prediction, combining the VQ-VAE and GIN models.
- **Directory Structure:**
  - This directory contains scripts and models related to protein analysis and interaction prediction using deep learning techniques.

## Instructions for Gemini

- The main scripts for training models are `vqvae_ver2_0717_2.py` and `VQVAE_VER1.py`.
- When modifying Python code, please adhere to PEP 8 style guidelines.
- The models rely on PyTorch, DGL, and Biopython. Ensure these dependencies are considered when running or modifying the code.
- The scripts expect PDB files as input, typically located in a directory specified by a `PDB_DIR` variable.
