import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import os
from Bio.PDB import PDBParser
from Bio.PDB.PDBExceptions import PDBConstructionWarning
import warnings
from tqdm import tqdm
import matplotlib.pyplot as plt

# 忽略PDB解析警告
warnings.filterwarnings("ignore", category=PDBConstructionWarning)


class ProteinDataset(Dataset):
    ATOM_TYPE_MAP = {
        'C': 0, 'CA': 1, 'CB': 2, 'CD': 3, 'CD1': 4, 'CD2': 5, 'CE': 6, 'CE1': 7,
        'CE2': 8, 'CE3': 9, 'CG': 10, 'CG1': 11, 'CG2': 12, 'CH2': 13, 'CZ': 14,
        'CZ2': 15, 'CZ3': 16, 'N': 17, 'ND1': 18, 'ND2': 19, 'NE': 20, 'NE1': 21,
        'NE2': 22, 'NH1': 23, 'NH2': 24, 'NZ': 25, 'O': 26, 'OD1': 27, 'OD2': 28,
        'OE1': 29, 'OE2': 30, 'OG': 31, 'OG1': 32, 'OH': 33, 'SD': 34, 'SG': 35,
        'H': 36, 'HA': 37, 'HB': 38, 'HD': 39, 'HE': 40, 'HH': 41, 'HZ': 42
    }

    def __init__(self, pdb_dir, max_atoms=1000):
        self.pdb_files = [
            os.path.join(pdb_dir, f)
            for f in os.listdir(pdb_dir) if f.endswith('.pdb')
        ]
        self.max_atoms = max_atoms
        self.parser = PDBParser(QUIET=True)
        print(f"Found {len(self.pdb_files)} PDB files.")

    def __len__(self):
        return len(self.pdb_files)

    def __getitem__(self, idx):
        path = self.pdb_files[idx]
        try:
            structure = self.parser.get_structure("prot", path)
            atoms = [atom for model in structure
                           for chain in model
                           for residue in chain
                           for atom in residue]
            coords = []
            types = []
            for atom in atoms:
                coords.append(atom.get_coord())
                t = atom.get_name().strip()
                types.append(self.ATOM_TYPE_MAP.get(t, len(self.ATOM_TYPE_MAP)))
            coords = np.array(coords, dtype=np.float32)
            types  = np.array(types, dtype=np.int64)

            # 截断或填充
            N = len(coords)
            if N > self.max_atoms:
                idxs = np.random.choice(N, self.max_atoms, replace=False)
                coords, types = coords[idxs], types[idxs]
                mask = np.ones(self.max_atoms, dtype=bool)
            else:
                pad = self.max_atoms - N
                coords = np.pad(coords, ((0,pad),(0,0)), 'constant', constant_values=0)
                types  = np.pad(types, (0,pad), 'constant', constant_values=len(self.ATOM_TYPE_MAP))
                mask = np.concatenate([np.ones(N, dtype=bool), np.zeros(pad, dtype=bool)])

            # 归一化
            mu = coords.mean(axis=0)
            st = coords.std(axis=0) + 1e-8
            coords = (coords - mu) / st

            return {
                'coords': torch.tensor(coords),
                'types':  torch.tensor(types),
                'mask':   torch.tensor(mask)
            }
        except Exception as e:
            # 出错时返回全零
            return {
                'coords': torch.zeros(self.max_atoms,3),
                'types':  torch.zeros(self.max_atoms,dtype=torch.long),
                'mask':   torch.zeros(self.max_atoms,dtype=torch.bool)
            }


class VectorQuantizer(nn.Module):
    def __init__(self, num_embeddings, embedding_dim, commitment_cost=0.25):
        super().__init__()
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
        self.commitment_cost = commitment_cost

        self.embeddings = nn.Embedding(num_embeddings, embedding_dim)
        nn.init.uniform_(self.embeddings.weight, -1.0, 1.0)

    def forward(self, inputs, mask=None):
        # inputs: [B,N,E]
        B, N, E = inputs.shape
        flat = inputs.view(-1, E)  # [B*N, E]

        # 计算距离
        d_sq = (flat.pow(2).sum(dim=1, keepdim=True)
               + self.embeddings.weight.pow(2).sum(dim=1)
               - 2 * flat @ self.embeddings.weight.t())
        encoding_inds = torch.argmin(d_sq, dim=1)
        quant = self.embeddings(encoding_inds).view(B, N, E)

        # straight-through
        quant_st = inputs + (quant - inputs).detach()

        # losses
        e_latent = F.mse_loss(quant.detach(), inputs)
        q_latent = F.mse_loss(quant, inputs.detach())
        loss = q_latent + self.commitment_cost * e_latent

        return quant_st, loss, encoding_inds.view(B, N)

class ProteinVQVAE(nn.Module):
    def __init__(self, num_atom_types, embedding_dim=128, num_embeddings=512, commitment=0.25):
        super().__init__()
        self.num_atom_types = num_atom_types
        
        # 编码器部分保持不变...
        self.atom_emb = nn.Embedding(num_atom_types+1, embedding_dim)
        self.coord_net = nn.Sequential(
            nn.Linear(3, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )
        self.fuse = nn.Sequential(
            nn.Linear(2*embedding_dim, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embedding_dim, nhead=8, dim_feedforward=512, batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=4)
        self.proj = nn.Sequential(
            nn.Linear(embedding_dim, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )
        self.vq = VectorQuantizer(num_embeddings, embedding_dim, commitment)

        # 修改解码器：同时输出坐标和原子类型
        self.decoder = nn.Sequential(
            nn.Linear(embedding_dim, 2 * embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(2 * embedding_dim)
        )
        self.coord_head = nn.Linear(2 * embedding_dim, 3)  # 坐标输出层
        self.type_head = nn.Linear(2 * embedding_dim, num_atom_types+1)  # 原子类型输出层

    def forward(self, coords, types, mask=None):
        # 编码过程保持不变...
        t = self.atom_emb(types)           # [B,N,E]
        c = self.coord_net(coords)         # [B,N,E]
        x = self.fuse(torch.cat([t, c], dim=-1))
        pad_mask = ~mask if mask is not None else None
        h = self.transformer(x, src_key_padding_mask=pad_mask)
        h = self.proj(h)
        quant, vq_loss, codes = self.vq(h, mask)
        
        # 解码过程
        dec_out = self.decoder(quant)
        recon_coords = self.coord_head(dec_out)  # [B,N,3]
        logits = self.type_head(dec_out)        # [B,N,num_atom_types+1]
        
        return recon_coords, logits, vq_loss, codes

def train_vqvae(pdb_dir, epochs=50, batch_size=4, max_atoms=1000, device='cuda'):
    dataset = ProteinDataset(pdb_dir, max_atoms)
    dl = DataLoader(dataset, batch_size=batch_size, shuffle=True, num_workers=4)

    model = ProteinVQVAE(
        num_atom_types=len(dataset.ATOM_TYPE_MAP),
        embedding_dim=64,
        num_embeddings=2048,
        commitment=0.25
    ).to(device)
    opt = optim.Adam(model.parameters(), lr=1e-3)
    losses = []

    for ep in range(epochs):
        model.train()
        total_loss = 0
        coord_loss_total = 0
        type_loss_total = 0
        vq_loss_total = 0
        for batch in tqdm(dl, desc=f"Epoch {ep+1}/{epochs}"):
            coords = batch['coords'].to(device)
            types  = batch['types'].to(device)
            mask   = batch['mask'].to(device)

            # 修改后的前向传播
            recon_coords, logits, vq_loss, codes = model(coords, types, mask)
            
            # 计算两种重构损失（只对真实原子计算）
            valid_mask = mask
            
            # 1. 坐标重构损失
            coord_loss = F.mse_loss(recon_coords[valid_mask], coords[valid_mask])
            
            # 2. 原子类型重构损失（交叉熵）
            # 注意：原子类型标签包含填充值（len(ATOM_TYPE_MAP)），但只计算真实原子
            type_loss = F.cross_entropy(
                logits[valid_mask], 
                types[valid_mask],
                ignore_index=len(dataset.ATOM_TYPE_MAP)  # 忽略填充的原子类型
            )
            
            # 总损失 = 坐标损失 + 原子类型损失 + VQ损失
            loss = coord_loss + type_loss + vq_loss

            opt.zero_grad()
            loss.backward()
            opt.step()

            # 记录各项损失
            coord_loss_total += coord_loss.item()
            type_loss_total += type_loss.item()
            vq_loss_total += vq_loss.item()
            total_loss += loss.item()

        avg = total_loss / len(dl)
        losses.append(avg)
        print(f"[{ep+1}/{epochs}] Total loss: {avg:.4f} | "
              f"Coord: {coord_loss_total/len(dl):.4f} | "
              f"Type: {type_loss_total/len(dl):.4f} | "
              f"VQ: {vq_loss_total/len(dl):.4f}")
        print(f"Codebook norms - Min: {model.vq.embeddings.weight.norm(dim=1).min().item():.2f} "
        f"Max: {model.vq.embeddings.weight.norm(dim=1).max().item():.2f} "
        f"Mean: {model.vq.embeddings.weight.norm(dim=1).mean().item():.2f}")

        # 打印本 epoch codebook 使用情况
        all_codes = codes[mask].view(-1).cpu()
        uniq = torch.unique(all_codes)
        print(f"  Used codebook entries: {len(uniq)}/{model.vq.num_embeddings}")

    # 绘制并保存损失曲线
    plt.plot(losses)
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('VQ-VAE Training Loss')
    plt.savefig('vqvae_loss.png')
    plt.close()

    return model


def encode_proteins(model, pdb_dir, max_atoms=1000, device='cuda'):
    dataset = ProteinDataset(pdb_dir, max_atoms)
    dl = DataLoader(dataset, batch_size=1, shuffle=False)
    codes_list = []

    model.eval()
    with torch.no_grad():
        for batch in tqdm(dl, desc="Encoding"):
            coords = batch['coords'].to(device)
            types  = batch['types'].to(device)
            mask   = batch['mask'].to(device)
            _, _, _, codes = model(coords, types, mask)
            valid = codes[mask].cpu().numpy()
            codes_list.append(valid)
    return codes_list


if __name__ == "__main__":
    # PDB_DIR = "/path/to/your/pdbs"
     # PDB_DIR = "/home/<USER>/code/MAPE-PPI/data/raw_data/all_pdbs"  # 替换为你的PDB文件目录
    PDB_DIR = "/home/<USER>/code/MAPE-PPI/data/raw_data/Arabidopsis_pdb"  # 替换为你的PDB文件目录
    
    DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

    print(f"Using device: {DEVICE}")
    model = train_vqvae(
        pdb_dir=PDB_DIR,
        epochs=30,
        batch_size=8,
        max_atoms=30000,
        device=DEVICE
    )
    torch.save(model.state_dict(), "protein_vqvae.pth")

    codes = encode_proteins(model, PDB_DIR, max_atoms=30000, device=DEVICE)
    for i, c in enumerate(codes[:5]):
        print(f"Protein {i+1}: unique codes = {np.unique(c).size}")
