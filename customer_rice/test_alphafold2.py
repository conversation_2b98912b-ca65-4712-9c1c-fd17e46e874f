import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader, Subset
import pandas as pd
import os
import pickle
from sklearn.model_selection import train_test_split
import numpy as np
from load_data import load_pairs, load_sequences,load_sequences2
import warnings
import torch
import torch.nn as nn
from torch.nn.utils.rnn import pad_sequence
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import os
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import confusion_matrix

from evalute_model import evaluate_model


# 特殊标记定义
PEPTIDE_TOKEN = 0
PROTEIN_TOKEN = 1
SEP_TOKEN = 2
model_save = "best_model.pth"
dataset_name="DeepPeppi"
dataset_name="Ar"
dataset_name="Rice"
is_balance = True
add_remaining_to_val = True

class ProteinPeptideDataset(Dataset):
    def __init__(self, positive_pairs, negative_pairs, feature_dir, max_length=4096):
        # 合并并过滤数据（保持之前的过滤逻辑）
        self.feature_dir = feature_dir
        self.max_length = max_length
        
        # 初始化数据列表
        self.processed_pairs = []
        
        # 合并正负样本
        all_pairs = pd.concat([
            positive_pairs.assign(label=1),
            negative_pairs.assign(label=0)
        ], ignore_index=True)
        count_in = 0
        count_filter = 0
        # 过滤并处理样本
        for _, row in all_pairs.iterrows():

            peptide_id, protein_id = row[0], row[1]
            peptide_path = os.path.join(feature_dir, f"{peptide_id}.npy")
            protein_path = os.path.join(feature_dir, f"{protein_id}.npy")
            
            if os.path.exists(peptide_path) and os.path.exists(protein_path):
                print(f"input{peptide_id},{protein_id}")
                count_in = count_in +1
                # self.processed_pairs.append((peptide_id, protein_id, row['label']))
                self.processed_pairs.append(row)
                
            else:
                print(f"filter{peptide_id},{protein_id}")
                print(f"path{peptide_path},{protein_path}")
                print(f"exists{os.path.exists(peptide_path)},{os.path.exists(protein_path)}")
                count_filter = count_filter + 1
        print(f"input:{count_in} filter: {count_filter}")
        self.processed_pairs = pd.DataFrame(self.processed_pairs)
    def __len__(self):
        return len(self.processed_pairs)

    def __getitem__(self, idx):
        pair = self.processed_pairs.iloc[idx]
        # print(pair["label"])
        peptide_id = pair[0]
        protein_id = pair[1]
        label = pair['label']
        
        # 加载特征
        peptide_feat = np.load(os.path.join(self.feature_dir, f"{peptide_id}.npy"))  # [L1, D]
        protein_feat = np.load(os.path.join(self.feature_dir, f"{protein_id}.npy"))  # [L2, D]
        
        # 添加类型标记
        peptide_marker = np.full((peptide_feat.shape[0], 1), PEPTIDE_TOKEN)
        protein_marker = np.full((protein_feat.shape[0], 1), PROTEIN_TOKEN)
        
        # 拼接特征和标记
        combined_seq = np.concatenate([
            np.concatenate([peptide_marker, peptide_feat], axis=1),
            np.array([[SEP_TOKEN] + [0]*peptide_feat.shape[1]]),  # 分隔符
            np.concatenate([protein_marker, protein_feat], axis=1)
        ])
        
        # 截断到最大长度
        if combined_seq.shape[0] > self.max_length:
            combined_seq = combined_seq[:self.max_length, :]
        
        return torch.tensor(combined_seq, dtype=torch.float32), torch.tensor(label)

def collate_fn(batch):
    # 动态填充批次数据
    sequences, labels = zip(*batch)
    
    # 创建注意力掩码
    lengths = [seq.shape[0] for seq in sequences]
    max_len = max(lengths)
    
    padded_sequences = []
    attention_masks = []
    
    for seq in sequences:
        padding = max_len - seq.shape[0]
        padded_seq = torch.cat([
            seq,
            torch.zeros(padding, seq.shape[1], dtype=seq.dtype)
        ])
        padded_sequences.append(padded_seq)
        
        mask = torch.cat([
            torch.ones(seq.shape[0]),
            torch.zeros(padding)
        ])
        attention_masks.append(mask)
    
    return (
        torch.stack(padded_sequences),
        torch.stack(attention_masks),
        torch.stack(labels)
    )

class InteractionTransformer(nn.Module):
    def __init__(self, input_dim=257, d_model=512, nhead=8, num_layers=4):
        super().__init__()
        # 标记嵌入层（类型标记+分隔符）
        self.marker_embedding = nn.Embedding(3, d_model)
        
        # 特征投影层
        self.feature_proj = nn.Linear(input_dim, d_model)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model*4,
            batch_first=True
        )
        self.encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Linear(d_model, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 1)
        )

    def forward(self, x, mask):
        # x: [batch_size, seq_len, input_dim+1]
        markers = x[:, :, 0].long()  # 提取标记列
        features = x[:, :, 1:]       # 原始特征
        
        # 嵌入处理
        marker_emb = self.marker_embedding(markers)  # [B, L, D]
        feature_emb = self.feature_proj(features)    # [B, L, D]
        
        # 合并嵌入
        combined = marker_emb + feature_emb
        
        # Transformer处理
        encoded = self.encoder(combined, src_key_padding_mask=~mask.bool())
        
        # 取分隔符位置的特征进行分类
        sep_positions = (markers == SEP_TOKEN).float()
        sep_features = (encoded * sep_positions.unsqueeze(-1)).sum(dim=1) / \
                       sep_positions.sum(dim=1, keepdim=True).clamp(min=1)
        
        return self.classifier(sep_features).squeeze(-1)

# # 使用示例
# if __name__ == "__main__":
#     # 初始化数据集和数据加载器
#     dataset = ProteinPeptideDataset(positive_pairs, negative_pairs, feature_dir)
#     train_loader = DataLoader(
#         dataset,
#         batch_size=32,
#         shuffle=True,
#         collate_fn=collate_fn,
#         num_workers=4
#     )
    
#     # 初始化模型
#     model = InteractionTransformer(input_dim=256)
    
#     # 训练循环需要处理mask
#     def train_model(...):
#         for inputs, masks, labels in train_loader:
#             inputs = inputs.to(device)
#             masks = masks.to(device)
#             labels = labels.to(device)
            
#             outputs = model(inputs, masks)
#     
def train_model(model, train_loader, val_loader, epochs=50, lr=1e-4):
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    criterion = nn.BCEWithLogitsLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    
    best_val_acc = 0
    for epoch in range(epochs):
        model.train()
        train_loss = []
        for inputs, masks, labels in train_loader:
            inputs, masks, labels = inputs.to(device), masks.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(inputs, masks)  # 添加mask作为输入
            loss = criterion(outputs, labels.float())  # 确保labels转为float
            loss.backward()
            optimizer.step()
            train_loss.append(loss.item())
        
        model.eval()
        val_loss = []
        correct = 0
        total = 0
        with torch.no_grad():
            for inputs, masks, labels in val_loader:
                inputs, masks, labels = inputs.to(device), masks.to(device), labels.to(device)
                outputs = model(inputs, masks)
                loss = criterion(outputs, labels.float())
                val_loss.append(loss.item())
                
                preds = torch.sigmoid(outputs) > 0.5
                correct += (preds == labels).sum().item()
                total += labels.size(0)
        
        val_acc = correct / total
        print(f"Epoch {epoch+1}/{epochs}")
        print(f"Train Loss: {np.mean(train_loss):.4f} | Val Loss: {np.mean(val_loss):.4f}")
        print(f"Val Acc: {val_acc:.4f}")
        
        if val_acc >= best_val_acc:
            best_val_acc = val_acc
            torch.save(model.state_dict(), model_save)
        print(f"BestAcc: {best_val_acc:.4f}")
# def evaluate_model(model, loader):
#     model.eval()
#     all_preds = []
#     all_labels = []
#     device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
#     model.to(device)
#     with torch.no_grad():
#             for inputs, masks, labels in loader:
#                 inputs, masks, labels = inputs.to(device), masks.to(device), labels.to(device)
#                 outputs = model(inputs, masks)
#                 preds = (torch.sigmoid(outputs) > 0.5).cpu().numpy()
#                 all_preds.extend(preds)
#                 all_labels.extend(labels.cpu().numpy())
                
#     cm = confusion_matrix(all_labels, all_preds)
#     print("\nConfusion Matrix:")
#     print(cm)
#     tn, fp, fn, tp = cm.ravel()
#     print(f"True Positives (TP): {tp}")
#     print(f"True Negatives (TN): {tn}")
#     print(f"False Positives (FP): {fp}")
#     print(f"False Negatives (FN): {fn}")
#     # 计算准确率
#     accuracy = (tp + tn) / (tp + tn + fp + fn)
#     print(f"Accuracy: {accuracy:.4f}")
#     return cm
    
import argparse
if __name__ == "__main__":
    # 创建 ArgumentParser 对象
    parser = argparse.ArgumentParser(description="命令行参数解析程序")

    # 添加 model_save 参数
    parser.add_argument(
        "--model_save",
        type=str,
        default="best_model.pth",
        help="模型保存路径，默认为 best_model.pth",
    )

    # 添加 dataset_name 参数，只能输入一次，如果没有输入，默认为 "DeepPeppi"
    parser.add_argument(
        "--dataset_name",
        type=str,
        default="DeepPeppi",
        help="数据集名称，默认为 DeepPeppi",
    )

    # 添加 is_balance 参数
    parser.add_argument(
        "--is_balance",
        type=lambda x: (str(x).lower() == "true"),
        default=True,
        help="是否平衡数据集，默认为 True",
    )

    # 添加 add_remaining_to_val 参数
    parser.add_argument(
        "--add_remaining_to_val",
        type=lambda x: (str(x).lower() == "true"),
        default=True,
        help="是否将剩余数据添加到验证集，默认为 True",
    )

    # 解析命令行参数
    args = parser.parse_args()

    # 将解析后的参数值赋给对应的变量
    model_save = args.model_save
    dataset_name = args.dataset_name
    is_balance = args.is_balance
    add_remaining_to_val = args.add_remaining_to_val

    # 打印变量值
    print("模型保存路径：", model_save)
    print("数据集名称：", dataset_name)
    print("是否平衡数据集：", is_balance)
    print("是否将剩余数据添加到验证集：", add_remaining_to_val)








    feature_dir = "../colab/single_represent"
    positive_csv = "./PepPI dataset/Arabidopsis thaliana/PepPIs ind.txt"
    negative_csv = "./PepPI dataset/Arabidopsis thaliana/non-PepPIs ind.txt"
    # 加载并平衡数据
    positive_pairs = pd.read_csv(positive_csv, sep='\t', header=None)
    negative_pairs = pd.read_csv(negative_csv, sep='\t', header=None)
    # 修改数据平衡部分的逻辑
    original_positive = pd.read_csv(positive_csv, sep='\t', header=None)
    original_negative = pd.read_csv(negative_csv, sep='\t', header=None)

    if dataset_name=="DeepPeppi":
        positive_csv = "./PepPI dataset/Arabidopsis thaliana/PepPIs ind.txt"
        negative_csv = "./PepPI dataset/Arabidopsis thaliana/non-PepPIs ind.txt"
        # model_save = "best_model_DeepPeppi.pth"
        # 加载并平衡数据
        positive_pairs = pd.read_csv(positive_csv, sep='\t', header=None)
        negative_pairs = pd.read_csv(negative_csv, sep='\t', header=None)
        # 修改数据平衡部分的逻辑
        original_positive = pd.read_csv(positive_csv, sep='\t', header=None)
        original_negative = pd.read_csv(negative_csv, sep='\t', header=None)
        # 统一处理蛋白质名称格式
        for df in [positive_pairs, negative_pairs]:
            for _, row in df.iterrows():
                # print(row)
                row[0] = row[0][:-2]
                row[1] = row[1][:-2]
        for df in [original_positive, original_negative]:
            for _, row in df.iterrows():
                # print(row)
                row[0] = row[0][:-2]
                row[1] = row[1][:-2]
    elif dataset_name=="Ar":
        positive_csv = "./PepPI dataset/Arabidopsis thaliana/postive.txt"
        negative_csv = "./PepPI dataset/Arabidopsis thaliana/negative.txt"
        # 加载并平衡数据
        positive_pairs = pd.read_csv(positive_csv, sep='     ', header=None)
        negative_pairs = pd.read_csv(negative_csv, sep='    ', header=None)
        # 修改数据平衡部分的逻辑
        original_positive = pd.read_csv(positive_csv, sep='     ', header=None)
        original_negative = pd.read_csv(negative_csv, sep='    ', header=None)
        for df in [positive_pairs, negative_pairs]:
            for _, row in df.iterrows():
                print(row[0])
                row[0] = row[0][:-2]
                row[1] = row[1][:-2]
                print(row[0])
        for df in [original_positive, original_negative]:
            for _, row in df.iterrows():
                # print(row)
                row[0] = row[0][:-2]
                row[1] = row[1][:-2]
        # model_save = "best_model_Ar.pth"
    elif dataset_name=="Rice":
        positive_csv = "./PepPI dataset/Arabidopsis thaliana/grind_positive.txt"
        negative_csv = "./PepPI dataset/Arabidopsis thaliana/grind_negative.txt"
        # model_save = "best_model_Rice.pth"
        # 加载并平衡数据
        positive_pairs = pd.read_csv(positive_csv, sep=' ', header=None)
        negative_pairs = pd.read_csv(negative_csv, sep=' ', header=None)
        # 加载并平衡数据
        original_positive = pd.read_csv(positive_csv, sep=' ', header=None)
        original_negative = pd.read_csv(negative_csv, sep=' ', header=None)

        model_save = "best_model_Rice.pth"
    
    # for df in [positive_pairs, negative_pairs]:
    #     for _, row in df.iterrows():
    #         print(row[0])
    #         row[0] = row[0][:-2]
    #         row[1] = row[1][:-2]
    #         print(row[0])
    # 数据平衡处理
    remaining_positive = pd.DataFrame()
    remaining_negative = pd.DataFrame()
    
    
    # 数据平衡处理
    remaining_positive = pd.DataFrame()
    remaining_negative = pd.DataFrame()
    if is_balance:
        min_samples = min(len(original_positive), len(original_negative))
        print(f"Balanced min_samples: {min_samples}")
        
        # 获取平衡样本
        balanced_positive = original_positive.sample(n=min_samples, random_state=42)
        balanced_negative = original_negative.sample(n=min_samples, random_state=42)
        
        # 获取剩余样本
        remaining_positive = original_positive.drop(balanced_positive.index)
        remaining_negative = original_negative.drop(balanced_negative.index)
        
        positive_pairs = balanced_positive
        negative_pairs = balanced_negative
    else:
        positive_pairs = original_positive
        negative_pairs = original_negative

    # 创建主数据集
    full_dataset = ProteinPeptideDataset(positive_pairs, negative_pairs, feature_dir)
    
    # 创建剩余数据集（如果有剩余样本）
    remaining_dataset = ProteinPeptideDataset(remaining_positive, remaining_negative, feature_dir)

    # 数据集划分（保持分层逻辑）
    indices = np.arange(len(full_dataset))
    labels = full_dataset.processed_pairs['label']
    
    # 第一次划分：训练集（80%）和临时集（20%）
    train_idx, temp_idx = train_test_split(
        indices,
        test_size=0.2,
        stratify=labels,
        random_state=42
    )
    
    # 第二次划分：测试集和验证集（各50%临时集）
    test_idx, val_idx = train_test_split(
        temp_idx,
        test_size=0.5,
        stratify=labels.iloc[temp_idx],  # 使用原始标签索引
        random_state=42
    )

    # 创建验证数据集组合
    from torch.utils.data import ConcatDataset
    
    original_val_subset = Subset(full_dataset, val_idx)
    
    # 根据模式组合验证集
    if add_remaining_to_val and is_balance and len(remaining_dataset) > 0:
        combined_val_dataset = ConcatDataset([original_val_subset, remaining_dataset])
        print(f"Added {len(remaining_dataset)} remaining samples to validation set")
    else:
        combined_val_dataset = original_val_subset

    # 创建数据加载器
    train_loader = DataLoader(
        Subset(full_dataset, train_idx),
        batch_size=32,
        shuffle=True,
        collate_fn=collate_fn,
        num_workers=4
    )
    test_loader = DataLoader(
        combined_val_dataset,  # 使用组合验证集
        batch_size=32,
        shuffle=True,
        collate_fn=collate_fn,
        num_workers=4
    )
    val_loader = DataLoader(
        Subset(full_dataset, test_idx),
        batch_size=32,
        shuffle=False,
        collate_fn=collate_fn,
        num_workers=4
    )
    # 获取输入维度
    sample_feat, _ = full_dataset[0]
    input_dim = sample_feat.size(-1) - 1
    # 初始化模型
    model = InteractionTransformer(input_dim=input_dim)
    
    # 训练模型
    train_model(model, train_loader, val_loader, epochs=50, lr=1e-4)
    
    # 加载最佳模型并评估测试集
    model.load_state_dict(torch.load(model_save))
    
    
    print("\nFinal Evaluation on Test Set:")
    evaluate_model(model, test_loader)
    print(f"min_samples {min_samples}")