#!/bin/bash

# 检查是否提供了Python文件名作为参数
if [ $# -eq 0 ]; then
    echo "错误：请提供要执行的Python文件名"
    echo "用法: $0 <python_script.py>"
    exit 1
fi

# 获取Python文件名（不含路径和扩展名）
PYTHON_FILE="$1"
GPU_DEVICE="$2"
PYTHON_BASENAME=$(basename "$PYTHON_FILE")
PYTHON_NAME="${PYTHON_BASENAME%.*}"

# 生成带时间的日志文件名（格式：python脚本名_年月日_时分秒.log）
LOG_FILE="${PYTHON_NAME}_$(date +"%Y%m%d_%H%M%S").log"

# 检查文件是否存在
if [ ! -f "$PYTHON_FILE" ]; then
    echo "错误：Python文件 '$PYTHON_FILE' 不存在"
    exit 1
fi

# 执行Python脚本并用tee双重输出
echo "开始执行:$GPU_DEVICE $PYTHON_FILE | 实时输出和日志: $LOG_FILE"
CUDA_VISIBLE_DEVICES=$GPU_DEVICE python "$PYTHON_FILE" | tee "$LOG_FILE"

# 获取执行状态
EXIT_STATUS=${PIPESTATUS[0]}
echo "执行完成 (状态: $EXIT_STATUS) | 日志保存在: $LOG_FILE"
exit $EXIT_STATUS