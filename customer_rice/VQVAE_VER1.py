
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import os
from Bio.PDB import PDBParser
from Bio.PDB.PDBExceptions import PDBConstructionWarning
import warnings
from tqdm import tqdm
import matplotlib.pyplot as plt
import dgl
from dgl.nn.pytorch import GINConv
from torch.utils.tensorboard import SummaryWriter
from sklearn.metrics import confusion_matrix, roc_auc_score, accuracy_score
import itertools
import pandas as pd
# Ignore PDB parsing warnings
warnings.filterwarnings("ignore", category=PDBConstructionWarning)

# --- Data Loading (from vqvae_ver2_0717_2.py) ---

class ProteinDataset(Dataset):
    ATOM_TYPE_MAP = {
        'C': 0, 'CA': 1, 'CB': 2, 'CD': 3, 'CD1': 4, 'CD2': 5, 'CE': 6, 'CE1': 7,
        'CE2': 8, 'CE3': 9, 'CG': 10, 'CG1': 11, 'CG2': 12, 'CH2': 13, 'CZ': 14,
        'CZ2': 15, 'CZ3': 16, 'N': 17, 'ND1': 18, 'ND2': 19, 'NE': 20, 'NE1': 21,
        'NE2': 22, 'NH1': 23, 'NH2': 24, 'NZ': 25, 'O': 26, 'OD1': 27, 'OD2': 28,
        'OE1': 29, 'OE2': 30, 'OG': 31, 'OG1': 32, 'OH': 33, 'SD': 34, 'SG': 35,
        'H': 36, 'HA': 37, 'HB': 38, 'HD': 39, 'HE': 40, 'HH': 41, 'HZ': 42
    }

    def __init__(self, pdb_dir, max_atoms=1000):
        self.pdb_files = [
            os.path.join(pdb_dir, f)
            for f in os.listdir(pdb_dir) if f.endswith('.pdb')
        ]
        self.max_atoms = max_atoms
        self.parser = PDBParser(QUIET=True)
        print(f"Found {len(self.pdb_files)} PDB files.")

    def __len__(self):
        return len(self.pdb_files)

    def __getitem__(self, idx):
        path = self.pdb_files[idx]
        try:
            structure = self.parser.get_structure("prot", path)
            atoms = [atom for model in structure
                           for chain in model
                           for residue in chain
                           for atom in residue]
            coords = []
            types = []
            for atom in atoms:
                coords.append(atom.get_coord())
                t = atom.get_name().strip()
                types.append(self.ATOM_TYPE_MAP.get(t, len(self.ATOM_TYPE_MAP)))
            coords = np.array(coords, dtype=np.float32)
            types  = np.array(types, dtype=np.int64)

            # Truncate or pad
            N = len(coords)
            if N > self.max_atoms:
                idxs = np.random.choice(N, self.max_atoms, replace=False)
                coords, types = coords[idxs], types[idxs]
                mask = np.ones(self.max_atoms, dtype=bool)
            else:
                pad = self.max_atoms - N
                coords = np.pad(coords, ((0,pad),(0,0)), 'constant', constant_values=0)
                types  = np.pad(types, (0,pad), 'constant', constant_values=len(self.ATOM_TYPE_MAP))
                mask = np.concatenate([np.ones(N, dtype=bool), np.zeros(pad, dtype=bool)])

            # Normalize
            mu = coords.mean(axis=0)
            st = coords.std(axis=0) + 1e-8
            coords = (coords - mu) / st

            return {
                'coords': torch.tensor(coords),
                'types':  torch.tensor(types),
                'mask':   torch.tensor(mask),
                'id': os.path.basename(path).replace('.pdb', '')
            }
        except Exception as e:
            return {
                'coords': torch.zeros(self.max_atoms,3),
                'types':  torch.zeros(self.max_atoms,dtype=torch.long),
                'mask':   torch.zeros(self.max_atoms,dtype=torch.bool),
                'id': 'error'
            }

# --- VQVAE Model (from vqvae_ver2_0717_2.py) ---

class VectorQuantizer(nn.Module):
    def __init__(self, num_embeddings, embedding_dim, commitment_cost=0.25):
        super().__init__()
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
        self.commitment_cost = commitment_cost

        self.embeddings = nn.Embedding(num_embeddings, embedding_dim)
        nn.init.uniform_(self.embeddings.weight, -1.0, 1.0)

    def forward(self, inputs, mask=None):
        B, N, E = inputs.shape
        flat = inputs.view(-1, E)

        d_sq = (flat.pow(2).sum(dim=1, keepdim=True)
               + self.embeddings.weight.pow(2).sum(dim=1)
               - 2 * flat @ self.embeddings.weight.t())
        encoding_inds = torch.argmin(d_sq, dim=1)
        quant = self.embeddings(encoding_inds).view(B, N, E)

        quant_st = inputs + (quant - inputs).detach()

        e_latent = F.mse_loss(quant.detach(), inputs)
        q_latent = F.mse_loss(quant, inputs.detach())
        loss = q_latent + self.commitment_cost * e_latent

        return quant_st, loss, encoding_inds.view(B, N)

class VQVAE(nn.Module):
    def __init__(self, num_atom_types, embedding_dim=128, num_embeddings=512, commitment=0.25):
        super().__init__()
        self.num_atom_types = num_atom_types
        
        self.atom_emb = nn.Embedding(num_atom_types+1, embedding_dim)
        self.coord_net = nn.Sequential(
            nn.Linear(3, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )
        self.fuse = nn.Sequential(
            nn.Linear(2*embedding_dim, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embedding_dim, nhead=8, dim_feedforward=512, batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=4)
        self.proj = nn.Sequential(
            nn.Linear(embedding_dim, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )
        self.vq = VectorQuantizer(num_embeddings, embedding_dim, commitment)

    def encode(self, coords, types, mask=None):
        t = self.atom_emb(types)
        c = self.coord_net(coords)
        x = self.fuse(torch.cat([t, c], dim=-1))
        pad_mask = ~mask if mask is not None else None
        h = self.transformer(x, src_key_padding_mask=pad_mask)
        h = self.proj(h)
        quant, _, _ = self.vq(h, mask)
        
        if mask is not None:
            protein_embedding = (quant * mask.unsqueeze(-1)).sum(dim=1) / mask.sum(dim=1).unsqueeze(-1)
        else:
            protein_embedding = quant.mean(dim=1)
            
        return protein_embedding

# --- GIN Model (from MAPE-PPI/src/models.py) ---

class GIN(torch.nn.Module):
    def __init__(self,  param):
        super(GIN, self).__init__()

        self.num_layers = param['ppi_num_layers']
        self.dropout = nn.Dropout(param['dropout_ratio'])
        self.layers = nn.ModuleList()
        
        self.layers.append(GINConv(nn.Sequential(nn.Linear(param['prot_hidden_dim'], param['ppi_hidden_dim']), 
                                                 nn.ReLU(), 
                                                 nn.Linear(param['ppi_hidden_dim'], param['ppi_hidden_dim']), 
                                                 nn.ReLU(), 
                                                 nn.BatchNorm1d(param['ppi_hidden_dim'])), 
                                                 aggregator_type='sum', 
                                                 learn_eps=True))

        for i in range(self.num_layers - 1):
            self.layers.append(GINConv(nn.Sequential(nn.Linear(param['ppi_hidden_dim'], param['ppi_hidden_dim']), 
                                                     nn.ReLU(), 
                                                     nn.Linear(param['ppi_hidden_dim'], param['ppi_hidden_dim']), 
                                                     nn.ReLU(), 
                                                     nn.BatchNorm1d(param['ppi_hidden_dim'])), 
                                                     aggregator_type='sum', 
                                                     learn_eps=True))

        self.linear = nn.Linear(param['ppi_hidden_dim'], param['ppi_hidden_dim'])
        self.fc = nn.Linear(param['ppi_hidden_dim'], param['output_dim'])

    def forward(self, g, x, ppi_list, idx):
        for l, layer in enumerate(self.layers):
            x = layer(g, x)
            x = self.dropout(x)

        x = F.dropout(F.relu(self.linear(x)), p=0.5, training=self.training)

        node_id = np.array(ppi_list)[idx]
        x1 = x[node_id[:, 0]]
        x2 = x[node_id[:, 1]]

        x = self.fc(torch.mul(x1, x2))
        
        return x

# --- PPI Prediction Model ---

class PPI_Prediction(nn.Module):
    def __init__(self, vqvae_model, gin_param, device, batch_size=4):
        super().__init__()
        self.vqvae = vqvae_model
        self.gin = GIN(gin_param)
        self.device = device
        self.batch_size = batch_size

    def forward(self, protein_dataset, ppi_graph, ppi_list, ppi_idx):
        # 1. Encode all proteins with VQVAE in batches
        protein_embeddings = torch.zeros(len(protein_dataset), GIN_PARAM['prot_hidden_dim'])
        protein_loader = DataLoader(protein_dataset, batch_size=self.batch_size, shuffle=False, num_workers=4)

        self.vqvae.eval() # Set VQVAE to evaluation mode for encoding
        with torch.no_grad():
            for i, data in enumerate(tqdm(protein_loader, desc="Encoding Proteins")):
                coords = data['coords'].to(self.device)
                types = data['types'].to(self.device)
                mask = data['mask'].to(self.device)
                
                # The VQVAE model is wrapped in DataParallel, so it can handle the batch
                embedding = self.vqvae.module.encode(coords, types, mask)
                start_idx = i * self.batch_size
                end_idx = start_idx + embedding.shape[0]
                protein_embeddings[start_idx:end_idx] = embedding.cpu()
        
        protein_embeddings = protein_embeddings.to(self.device)

        # 2. Predict PPI with GIN
        self.gin.train() # Set GIN back to training mode
        predictions = self.gin(ppi_graph, protein_embeddings, ppi_list, ppi_idx)
        return predictions

# --- Utility Functions ---

def plot_confusion_matrix(cm, classes, normalize=False, title='Confusion matrix', cmap=plt.cm.Blues):
    fig, ax = plt.subplots()
    if normalize:
        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
    
    im = ax.imshow(cm, interpolation='nearest', cmap=cmap)
    ax.set_title(title)
    fig.colorbar(im)
    tick_marks = np.arange(len(classes))
    ax.set_xticks(tick_marks)
    ax.set_xticklabels(classes, rotation=45)
    ax.set_yticks(tick_marks)
    ax.set_yticklabels(classes)

    fmt = '.2f' if normalize else 'd'
    thresh = cm.max() / 2.
    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):
        ax.text(j, i, format(cm[i, j], fmt),
                 horizontalalignment="center",
                 color="white" if cm[i, j] > thresh else "black")

    fig.tight_layout()
    ax.set_ylabel('True label')
    ax.set_xlabel('Predicted label')
    return fig

# --- Main Training and Evaluation Script ---

from sklearn.model_selection import train_test_split

if __name__ == '__main__':
    # --- Configurable Parameters ---
    DATASET_NAME = "Ar"  # Options: "Ar", "Rice", "DeepPeppi"
    IS_BALANCE = True
    ADD_REMAINING_TO_VAL = True
    MODEL_SAVE_PATH = f"best_model_{DATASET_NAME}.pth"

    # --- Set paths and loading parameters based on dataset name ---
    if DATASET_NAME == "Ar":
        PDB_DIR = "/home/<USER>/code/MAPE-PPI/data/raw_data/Arabidopsis_pdb"
        PPI_DATA_DIR = "/home/<USER>/code/customer_rice/PepPI dataset/Arabidopsis thaliana"
        positive_file = os.path.join(PPI_DATA_DIR, "postive.txt")
        negative_file = os.path.join(PPI_DATA_DIR, "negative.txt")
        pos_sep, neg_sep = '     ', '    '
    elif DATASET_NAME == "Rice":
        PDB_DIR = "/home/<USER>/code/MAPE-PPI/data/raw_data/all_pdbs"
        PPI_DATA_DIR = "/home/<USER>/code/customer_rice/PepPI dataset/Arabidopsis thaliana"
        positive_file = os.path.join(PPI_DATA_DIR, "grind_positive.txt")
        negative_file = os.path.join(PPI_DATA_DIR, "grind_negative.txt")
        pos_sep, neg_sep = ' ', ' '
    elif DATASET_NAME == "DeepPeppi":
        PDB_DIR = "/home/<USER>/code/MAPE-PPI/data/raw_data/all_pdbs"
        PPI_DATA_DIR = "/home/<USER>/code/customer_rice/PepPI dataset/Arabidopsis thaliana"
        positive_file = os.path.join(PPI_DATA_DIR, "PepPIs ind.txt")
        negative_file = os.path.join(PPI_DATA_DIR, "non-PepPIs ind.txt")
        pos_sep, neg_sep = '\t', '\t'
    else:
        raise ValueError(f"Unknown dataset: {DATASET_NAME}")

    MAX_ATOMS = 2000
    BATCH_SIZE = 16
    EPOCHS = 100
    LEARNING_RATE = 1e-4
    DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
    
    VQVAE_EMBEDDING_DIM = 64
    VQVAE_NUM_EMBEDDINGS = 1024
    
    GIN_PARAM = {
        'ppi_num_layers': 3,
        'dropout_ratio': 0.5,
        'prot_hidden_dim': VQVAE_EMBEDDING_DIM,
        'ppi_hidden_dim': 128,
        'output_dim': 2
    }

    writer = SummaryWriter(log_dir=f'runs/ppi_prediction_{DATASET_NAME}')

    protein_dataset = ProteinDataset(PDB_DIR, max_atoms=MAX_ATOMS)
    protein_ids = [p['id'] for p in protein_dataset]
    protein_id_map = {name: i for i, name in enumerate(protein_ids)}

    # --- Load and Process PPI Data ---
    positive_pairs_df = pd.read_csv(positive_file, sep=pos_sep, header=None, engine='python')
    negative_pairs_df = pd.read_csv(negative_file, sep=neg_sep, header=None, engine='python')

    all_pairs = []
    all_labels = []

    for _, row in positive_pairs_df.iterrows():
        p1, p2 = str(row[0]), str(row[1])
        if DATASET_NAME == 'DeepPeppi': p1, p2 = p1[:-2], p2[:-2]
        if p1 in protein_id_map and p2 in protein_id_map:
            all_pairs.append([protein_id_map[p1], protein_id_map[p2]])
            all_labels.append(1)

    for _, row in negative_pairs_df.iterrows():
        p1, p2 = str(row[0]), str(row[1])
        if DATASET_NAME == 'DeepPeppi': p1, p2 = p1[:-2], p2[:-2]
        if p1 in protein_id_map and p2 in protein_id_map:
            all_pairs.append([protein_id_map[p1], protein_id_map[p2]])
            all_labels.append(0)

    print(f"Loaded {len(all_pairs)} total PPI pairs.")

    # --- Dataset Splitting and Balancing ---
    indices = np.arange(len(all_pairs))
    labels_np = np.array(all_labels)

    train_idx, temp_idx, train_labels, temp_labels = train_test_split(
        indices, labels_np, test_size=0.2, stratify=labels_np, random_state=42)
    
    val_idx, test_idx, _, _ = train_test_split(
        temp_idx, temp_labels, test_size=0.5, stratify=temp_labels, random_state=42)

    if IS_BALANCE:
        print("Balancing the training dataset...")
        train_pos_idx = train_idx[labels_np[train_idx] == 1]
        train_neg_idx = train_idx[labels_np[train_idx] == 0]
        min_samples = min(len(train_pos_idx), len(train_neg_idx))
        
        balanced_pos_idx = np.random.choice(train_pos_idx, min_samples, replace=False)
        balanced_neg_idx = np.random.choice(train_neg_idx, min_samples, replace=False)
        
        remaining_neg_idx = np.setdiff1d(train_neg_idx, balanced_neg_idx)
        
        train_idx = np.concatenate([balanced_pos_idx, balanced_neg_idx])
        np.random.shuffle(train_idx)

        print(f"Balanced training set to {len(train_idx)} samples.")

        if ADD_REMAINING_TO_VAL and len(remaining_neg_idx) > 0:
            val_idx = np.concatenate([val_idx, remaining_neg_idx])
            print(f"Added {len(remaining_neg_idx)} remaining negative samples to validation set.")

    # --- DGL Graph and Final Labels Tensor ---
    ppi_graph = dgl.graph((torch.tensor([p[0] for p in all_pairs]), torch.tensor([p[1] for p in all_pairs])), num_nodes=len(protein_dataset))
    ppi_graph = dgl.add_self_loop(ppi_graph).to(DEVICE)
    labels_tensor = torch.tensor(all_labels, dtype=torch.long).to(DEVICE)

    # --- Model Initialization ---
    device = torch.device(DEVICE)
    vqvae = VQVAE(len(ProteinDataset.ATOM_TYPE_MAP), VQVAE_EMBEDDING_DIM, VQVAE_NUM_EMBEDDINGS)
    if torch.cuda.device_count() > 1:
        print(f"Using {torch.cuda.device_count()} GPUs!")
        vqvae = nn.DataParallel(vqvae)
    vqvae.to(device);

    model = PPI_Prediction(vqvae, GIN_PARAM, device, batch_size=BATCH_SIZE).to(device)
    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)
    criterion = nn.CrossEntropyLoss()

    # --- Training Loop ---
    best_val_auc = 0.0
    for epoch in range(EPOCHS):
        model.train()
        optimizer.zero_grad()
        predictions = model(protein_dataset, ppi_graph, all_pairs, train_idx)
        loss = criterion(predictions, labels_tensor[train_idx])
        loss.backward()
        optimizer.step()
        
        print(f"Epoch {epoch+1}/{EPOCHS}, Loss: {loss.item():.4f}")
        writer.add_scalar('Loss/train', loss.item(), epoch)

        # --- Validation ---
        if (epoch + 1) % 5 == 0:
            model.eval()
            with torch.no_grad():
                val_preds = model(protein_dataset, ppi_graph, all_pairs, val_idx)
                val_loss = criterion(val_preds, labels_tensor[val_idx])
                
                probs = F.softmax(val_preds, dim=1)[:, 1]
                predicted_labels = torch.argmax(val_preds, dim=1)
                
                true_labels = labels_tensor[val_idx].cpu().numpy()
                predicted_labels = predicted_labels.cpu().numpy()
                probs = probs.cpu().numpy()

                acc = accuracy_score(true_labels, predicted_labels)
                auc = roc_auc_score(true_labels, probs)
                
                print(f"Validation Loss: {val_loss.item():.4f}, Accuracy: {acc:.4f}, AUC: {auc:.4f}")
                writer.add_scalar('Loss/validation', val_loss.item(), epoch)
                writer.add_scalar('Accuracy/validation', acc, epoch)
                writer.add_scalar('AUC/validation', auc, epoch)

                if auc > best_val_auc:
                    best_val_auc = auc
                    torch.save(model.state_dict(), MODEL_SAVE_PATH)
                    print(f"New best model saved to {MODEL_SAVE_PATH} with AUC: {auc:.4f}")

    writer.close()
    print("Training finished.")

    # --- Final Evaluation on Test Set ---
    print("\n--- Final Evaluation on Test Set ---")
    model.load_state_dict(torch.load(MODEL_SAVE_PATH))
    model.eval()
    with torch.no_grad():
        test_preds = model(protein_dataset, ppi_graph, all_pairs, test_idx)
        test_probs = F.softmax(test_preds, dim=1)[:, 1]
        test_predicted_labels = torch.argmax(test_preds, dim=1)

        true_labels = labels_tensor[test_idx].cpu().numpy()
        predicted_labels = test_predicted_labels.cpu().numpy()
        
        cm = confusion_matrix(true_labels, predicted_labels)
        tn, fp, fn, tp = cm.ravel()
        final_accuracy = accuracy_score(true_labels, predicted_labels)

        print("\nConfusion Matrix:")
        print(cm)
        print(f"True Positives (TP): {tp}")
        print(f"True Negatives (TN): {tn}")
        print(f"False Positives (FP): {fp}")
        print(f"False Negatives (FN): {fn}")
        print(f"\nFinal Test Accuracy: {final_accuracy:.4f}")
