
# Gemini Project Configuration for dMaSIF

This file provides instructions for the Gemini agent to interact with the dMaSIF project.

## Build & Test Commands

- **Build:** `echo "No build command specified. Dependencies are installed via pip."`
- **Test:** `echo "No specific test command provided. Validation is part of the training script."`
- **Linter:** `echo "No linter specified."`

## Project Overview

- **Primary Language:** Python
- **Core Frameworks:** PyTorch, PyTorch Geometric, PyKeops
- **Purpose:** dMaSIF (differentiable Molecular Surface Interaction Fingerprinting) is a geometric deep learning framework for fast, end-to-end learning on protein surfaces. It takes raw atomic coordinates (e.g., from PDB files) as input to perform tasks like interaction site identification and protein-protein interaction (PPI) prediction.

- **Key Files:**
  - `main_training.py`: The main script for training models.
  - `main_inference.py`: The main script for running inference with pre-trained models.
  - `model.py`: Defines the neural network architecture.
  - `geometry_processing.py`: Contains the core logic for on-the-fly surface generation and geometric convolutions.
  - `Arguments.py`: Defines the command-line arguments for training and inference.
  - `dataloading.py`: Handles the loading and preprocessing of protein data.

## Instructions for Gemini

1.  **Primary Goal:** The main goal is typically to predict interaction sites on a protein surface or to predict whether two proteins will interact.
2.  **Input Data:** The fundamental input for this project is one or more protein structures in PDB format.
3.  **Training vs. Inference:**
    - To train a new model, use `python main_training.py` with the appropriate flags from `Arguments.py`.
    - To make a prediction on new data using an existing model, the primary script to use is `python main_inference.py`. You will need to specify the path to the PDB file(s) and the pre-trained model to use.
4.  **Core Logic:** The unique strength of dMaSIF is in `geometry_processing.py`. This file handles the conversion from a point cloud of atoms to a molecular surface and applies the custom convolutions on-the-fly.
5.  **Simplifying for a Demo:** When asked to create a demo, the goal is to extract the core prediction logic from `main_inference.py`. This involves:
    - Hardcoding the path to a pre-trained model.
    - Simplifying the data loading part to accept just a path to a PDB file.
    - Removing all argument parsing, training-related code, and performance benchmarking.
    - The output should be a clear prediction (e.g., interaction scores or site coordinates).
