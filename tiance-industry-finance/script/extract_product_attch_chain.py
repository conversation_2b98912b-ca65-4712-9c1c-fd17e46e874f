import asyncio
import json
import re
import pandas as pd
from openpyxl import load_workbook
import sys
from pathlib import Path
from tqdm import tqdm

from datetime import datetime
from sqlalchemy import Column, String, Text, Integer, DateTime
from sqlalchemy.ext.declarative import declarative_base
from pydantic import BaseModel, Field
from typing import List, Optional

project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))
from configs.collection_config import CollectionConfig
from entity.message_entity import SystemMessage
from utils.llm_model_util import Llm_Service, MessageConverter, UserMessage
from service.rerank_service import Rerank_Service
from utils.milvus_util import MilvusUtil
from utils.mongodb_util import MongodbUtil
from utils.text_utils import TextUtil
import jieba
from sqlalchemy.orm import Session
from sqlalchemy import desc, text
from service.kb_service import KbService

def read_excel_file(file_path,sheet_name,columns_to_read):
    df = pd.read_excel(file_path, sheet_name=sheet_name, usecols=columns_to_read)
    return df


def  extract_product_llm_one(key,content,llm_service,industry,chain):

    model = "DeepSeek-R1-Distill-Qwen-32B"
    prompt = f"""
    
    # 角色
    你是一位银行对公部门的产业研究专家，现正在分析{industry}产业链下有哪些公司，请根据根据现有专利标题、专利摘要补充{industry}产业链下每个末级产业链环节点以及产品/商品/业务下的公司名称
    #公司名称列说明：
    指每个有内容的产品/商品/业务下，当前市场上哪些企业生产或销售该环节实体涉及的具体产品或商品
    #技能：
    1、请针对提供内容及以上说明，并且结合产业链结构，补充产业链所在行每个有内容的末级产业链环节以及产品/商品关键词的公司名称列
    2、产业链环节关联公司必须提供原文来源依据，来源可靠可追溯（关键词所在原文最相关的段落）），如果产业链环节下没有关联公司，不用返回。
    3、产业链环节关联公司必须提供推理过程（为什么该末级环节或者产品属于该公司的思考过程），如果产业链环节下没有关联公司，不用返回。
    3、在专利列表的标题或摘要里，找到对应的产业链环节以及产品/商品/业务关键词，并在回复中给出专利对应的申请号
    4、用中文思考，中文输出；
    
    ===输出格式===
    [{{"申请号":"申请号1","原文依据"："原文依据1","推理过程":"推理过程"}}]
    ===示例结束===

    ## 限制
    - 只专注于从给定的【专利列表】中分析，不考虑其他无关内容。
    - 严格按照要求的格式输出，确保格式正确、完整;只允许输出申请号,严禁输出任何其它无关内容。
    【产品/商品/业务关键词】
    对应末级环节：{chain}
    产品/商品/业务关键词：{key}
    【专利列表】
    {content}
    #{industry}产业链结构如下：  
    产业类型	上中下游	一级产业链环节	二级产业链环节	三级产业链环节	四级产业链环节	五级产业链环节	六级产业链环节	末级环节
    低空经济								
    低空经济	上游							
    低空经济	上游	低空基础设施						低空基础设施
    低空经济	上游	低空基础设施	通航机场设施、设备					通航机场设施、设备
    低空经济	上游	低空基础设施	通航机场设施、设备	空中交通管制设施	短波通信系统			短波通信系统
    低空经济	上游	低空基础设施	通航机场设施、设备	空中交通管制设施	广播式自动相关检视			广播式自动相关检视
    低空经济	上游	低空基础设施	通航机场设施、设备	空中交通管制设施	航行情报信息终端			航行情报信息终端
    低空经济	上游	低空基础设施	通航机场设施、设备	空中交通管制设施	甚高频通信系统			甚高频通信系统
    低空经济	上游	低空基础设施	通航机场设施、设备	空中交通管制设施	多通道数字记录仪系统			多通道数字记录仪系统
    低空经济	上游	低空基础设施	通航机场设施、设备	空中交通管制设施	塔台			塔台
    低空经济	上游	低空基础设施	通航机场设施、设备	空中交通管制设施	气象信息			气象信息
    低空经济	上游	低空基础设施	通航机场设施、设备	空中交通管制设施	导航设施			导航设施
    低空经济	上游	低空基础设施	通航机场设施、设备	空中交通管制设施	空管用房			空管用房
    低空经济	上游	低空基础设施	通航机场设施、设备	空中交通管制设施	时钟系统			时钟系统
    低空经济	上游	低空基础设施	通航机场设施、设备	空中交通管制设施	手持或车载台无线对讲系统			手持或车载台无线对讲系统
    低空经济	上游	低空基础设施	通航机场设施、设备	安全保卫设施				安全保卫设施
    低空经济	上游	低空基础设施	通航机场设施、设备	安全保卫设施	围栏			围栏
    低空经济	上游	低空基础设施	通航机场设施、设备	安全保卫设施	巡场路			巡场路
    低空经济	上游	低空基础设施	通航机场设施、设备	安全保卫设施	应急车辆			应急车辆
    低空经济	上游	低空基础设施	通航机场设施、设备	目视助航设施				目视助航设施
    低空经济	上游	低空基础设施	通航机场设施、设备	目视助航设施	助航灯光/标记牌			助航灯光/标记牌
    低空经济	上游	低空基础设施	通航机场设施、设备	目视助航设施	标志/标志物			标志/标志物
    低空经济	上游	低空基础设施	通航机场设施、设备	目视助航设施	风向标			风向标
    低空经济	上游	低空基础设施	通航机场设施、设备	目视助航设施	着陆方向标			着陆方向标
    低空经济	上游	低空基础设施	通航机场设施、设备	飞行场地				飞行场地
    低空经济	上游	低空基础设施	通航机场设施、设备	飞行场地	水上机场飞行场地			水上机场飞行场地
    低空经济	上游	低空基础设施	通航机场设施、设备	飞行场地	供直升机运行的机场			供直升机运行的机场
    低空经济	上游	低空基础设施	通航机场设施、设备	飞行场地	供固定翼航空器运行的陆地机场			供固定翼航空器运行的陆地机场
    低空经济	上游	低空基础设施	通航机场设施、设备	飞行场地	消防及应急救援设施			消防及应急救援设施
    低空经济	上游	低空基础设施	通航机场设施、设备	服务保障设施				服务保障设施
    低空经济	上游	低空基础设施	低空新型基础设施					低空新型基础设施
    低空经济	上游	低空基础设施	低空新型基础设施	低空网络设施				低空网络设施
    低空经济	上游	低空基础设施	低空新型基础设施	低空网络设施	低空飞行器通信			低空飞行器通信
    低空经济	上游	低空基础设施	低空新型基础设施	低空网络设施	低空飞行器定位			低空飞行器定位
    低空经济	上游	低空基础设施	低空新型基础设施	低空网络设施	低空飞行器导航			低空飞行器导航
    低空经济	上游	低空基础设施	低空新型基础设施	低空网络设施	低空飞行器监控			低空飞行器监控
    低空经济	上游	低空基础设施	低空新型基础设施	低空网络设施	低空飞行器识别			低空飞行器识别
    低空经济	上游	低空基础设施	低空新型基础设施	低空数据设施				低空数据设施
    低空经济	上游	低空基础设施	低空新型基础设施	低空数据设施	低空数据中心			低空数据中心
    低空经济	上游	低空基础设施	低空新型基础设施	低空数据设施	低空数据平台			低空数据平台
    低空经济	上游	低空基础设施	低空新型基础设施	低空数据设施	低空数据采集			低空数据采集
    低空经济	上游	低空基础设施	低空新型基础设施	低空监管设施				低空监管设施
    低空经济	上游	低空基础设施	低空新型基础设施	低空监管设施	无人机监管设施	无人机反制平台		无人机反制平台
    低空经济	上游	低空基础设施	低空新型基础设施	低空监管设施	无人机监管设施	无人机运行调度系统		无人机运行调度系统
    低空经济	上游	低空基础设施	低空新型基础设施	低空监管设施	无人机监管设施	无人机空中交通管理系统		无人机空中交通管理系统
    低空经济	上游	低空基础设施	低空新型基础设施	低空监管设施	无人机监管设施	无人机综合监管平台		无人机综合监管平台
    低空经济	上游	低空基础设施	低空新型基础设施	低空监管设施	有人机监管设施			有人机监管设施
    低空经济	上游	低空基础设施	低空新型基础设施	低空监管设施	有人机监管设施	有人机运行调度系统		有人机运行调度系统
    低空经济	上游	低空基础设施	低空新型基础设施	低空监管设施	有人机监管设施	有人机空中交通管理系统		有人机空中交通管理系统
    低空经济	上游	低空基础设施	低空新型基础设施	无人机起降场地				无人机起降场地
    低空经济	上游	低空基础设施	低空新型基础设施	无人机起降场地	起降枢纽			起降枢纽
    低空经济	上游	低空基础设施	低空新型基础设施	无人机起降场地	起降场			起降场
    低空经济	上游	低空基础设施	低空新型基础设施	无人机起降场地	起降点			起降点
    低空经济	上游	低空基础设施	低空新型基础设施	新能源航空器能源基础设施				新能源航空器能源基础设施
    低空经济	上游	低空基础设施	低空新型基础设施	新能源航空器能源基础设施	电动飞机充换电设施			电动飞机充换电设施
    低空经济	上游	低空基础设施	低空新型基础设施	新能源航空器能源基础设施	氢能源飞行器加氢站			氢能源飞行器加氢站
    低空经济	上游	研发						研发
    低空经济	上游	研发	CAX					CAX
    低空经济	上游	研发	EDA					EDA
    低空经济	上游	研发	PLM					PLM
    低空经济	上游	关键原材料						关键原材料
    低空经济	上游	关键原材料	钢材					钢材
    低空经济	上游	关键原材料	铝合金					铝合金
    低空经济	上游	关键原材料	工程塑料					工程塑料
    低空经济	上游	关键原材料	陶瓷基材					陶瓷基材
    低空经济	上游	关键原材料	碳纤维					碳纤维
    低空经济	上游	关键原材料	玻璃纤维					玻璃纤维
    低空经济	上游	关键原材料	树脂基材					树脂基材
    低空经济	上游	关键原材料	复合材料					复合材料
    低空经济	上游	基础零部件						基础零部件
    低空经济	上游	基础零部件	芯片					芯片
    低空经济	上游	基础零部件	板卡					板卡
    低空经济	上游	基础零部件	电池					电池
    低空经济	上游	基础零部件	电机					电机
    低空经济	上游	基础零部件	陀螺					陀螺
    低空经济	中游	低空飞行器制造						低空飞行器制造
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件					低空飞行器关键系统及零部件
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	低空飞行器动力系统				低空飞行器动力系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	低空飞行器动力系统	航空发动机	中小微航空发动机		中小微航空发动机
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	低空飞行器动力系统	三电系统			三电系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	低空飞行器动力系统	三电系统	高能量密度锂电池		高能量密度锂电池
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	低空飞行器动力系统	三电系统	超导电机		超导电机
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	低空飞行器动力系统	三电系统	高功率重比电机驱动控制器		高功率重比电机驱动控制器
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	低空飞行器动力系统	混合动力系统			混合动力系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	低空飞行器动力系统	混合动力系统	中小型涡轴发动机		中小型涡轴发动机
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	低空飞行器动力系统	混合动力系统	涡桨发动机		涡桨发动机
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	低空飞行器动力系统	混合动力系统	活塞发动机		活塞发动机
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	低空飞行器动力系统	高功率密度燃料电池			高功率密度燃料电池
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统				机载系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载感知系统			机载感知系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载感知系统	环境传感器		环境传感器
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载感知系统	环境传感器	温度传感器	温度传感器
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载感知系统	环境传感器	湿度传感器	湿度传感器
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载感知系统	环境传感器	压力传感器	压力传感器
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载感知系统	环境传感器	热流传感器	热流传感器
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载感知系统	姿态传感器		姿态传感器
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载感知系统	姿态传感器	加速度计	加速度计
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载感知系统	姿态传感器	陀螺仪	陀螺仪
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载感知系统	姿态传感器	GPS	GPS
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载感知系统	姿态传感器	罗盘	罗盘
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载感知系统	激光传感器		激光传感器
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载通信导航系统			机载通信导航系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载通信导航系统	地面通信系统		地面通信系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载通信导航系统	卫星通信系统		卫星通信系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载通信导航系统	无线电通信系统		无线电通信系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载通信导航系统	导航系统		导航系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载通信导航系统	智能座舱		智能座舱
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	机载通信导航系统	智能维护监测系统		智能维护监测系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	机载系统	其他系统			其他系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	飞控系统				飞控系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	飞控系统	自动飞行控制系统			自动飞行控制系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	飞控系统	无人驾驶飞控系统			无人驾驶飞控系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	飞控系统	其他飞控系统			其他飞控系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	飞控系统	其他飞控系统	自动驾驶仪		自动驾驶仪
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	飞控系统	其他飞控系统	飞行管理系统		飞行管理系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	飞控系统	其他飞控系统	姿态稳定和控制		姿态稳定和控制
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	飞控系统	其他飞控系统	任务设备管理		任务设备管理
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	飞控系统	其他飞控系统	应急控制		应急控制
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	飞控系统	其他飞控系统	电子飞行控制系统		电子飞行控制系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	飞控系统	其他飞控系统	液压飞控系统		液压飞控系统
    低空经济	中游	低空飞行器制造	低空飞行器关键系统及零部件	飞控系统	其他飞控系统	机械飞控系统		机械飞控系统
    低空经济	中游	低空飞行器制造	低空飞行器整机制造					低空飞行器整机制造
    低空经济	中游	低空飞行器制造	低空飞行器整机制造	轻小型固定翼飞机				轻小型固定翼飞机
    低空经济	中游	低空飞行器制造	低空飞行器整机制造	民用直升机				民用直升机
    低空经济	中游	低空飞行器制造	低空飞行器整机制造	氢能源飞机				氢能源飞机
    低空经济	中游	低空飞行器制造	低空飞行器整机制造	eVTOL				eVTOL
    低空经济	中游	低空飞行器制造	低空飞行器整机制造	无人机				无人机
    低空经济	中游	载荷						载荷
    低空经济	中游	载荷	摄像机					摄像机
    低空经济	中游	载荷	传感器					传感器
    低空经济	中游	载荷	传感器	激光雷达				激光雷达
    低空经济	中游	载荷	云台					云台
    低空经济	中游	地面系统						地面系统
    低空经济	中游	地面系统	遥控监测					遥控监测
    低空经济	中游	地面系统	系统监控					系统监控
    低空经济	中游	地面系统	数据处理					数据处理
    低空经济	中游	地面系统	起降系统					起降系统
    低空经济	中游	地面系统	辅助设备					辅助设备
    低空经济	中游	地面系统	指挥系统					指挥系统
    低空经济	下游							
    低空经济	下游	低空运营服务						低空运营服务
    低空经济	下游	低空运营服务	低空运营场景					低空运营场景
    低空经济	下游	低空运营服务	低空运营场景	城市应用				城市应用
    低空经济	下游	低空运营服务	低空运营场景	城市应用	城市安全			城市安全
    低空经济	下游	低空运营服务	低空运营场景	城市应用	城市消防			城市消防
    低空经济	下游	低空运营服务	低空运营场景	城市应用	国土测绘			国土测绘
    低空经济	下游	低空运营服务	低空运营场景	城市应用	气象探测			气象探测
    低空经济	下游	低空运营服务	低空运营场景	城市应用	环境保护			环境保护
    低空经济	下游	低空运营服务	低空运营场景	城市应用	空中游览			空中游览
    低空经济	下游	低空运营服务	低空运营场景	城市应用	航拍摄影			航拍摄影
    低空经济	下游	低空运营服务	低空运营场景	城市应用	空中广告			空中广告
    低空经济	下游	低空运营服务	低空运营场景	城市应用	航空表演			航空表演
    低空经济	下游	低空运营服务	低空运营场景	城市应用	私人飞行			私人飞行
    低空经济	下游	低空运营服务	低空运营场景	低空文旅				低空文旅
    低空经济	下游	低空运营服务	低空运营场景	低空文旅	低空租赁			低空租赁
    低空经济	下游	低空运营服务	低空运营场景	低空文旅	低空金融			低空金融
    低空经济	下游	低空运营服务	低空运营场景	低空文旅	低空会展			低空会展
    低空经济	下游	低空运营服务	低空运营场景	低空文旅	低空文创			低空文创
    低空经济	下游	低空运营服务	低空运营场景	低空延伸服务				低空延伸服务
    低空经济	下游	低空运营服务	低空运营场景	低空延伸服务	无人机竞速			无人机竞速
    低空经济	下游	低空运营服务	低空运营场景	低空延伸服务	无人驾驶			无人驾驶
    低空经济	下游	低空运营服务	低空运营场景	低空飞行培训				低空飞行培训
    低空经济	下游	低空运营服务	低空运营场景	低空飞行培训	飞行培训			飞行培训
    低空经济	下游	低空运营服务	低空运营场景	低空飞行培训	航空法律培训			航空法律培训
    低空经济	下游	低空运营服务	低空运营场景	低空飞行培训	驾驶证考务服务			驾驶证考务服务
    低空经济	下游	低空运营服务	低空飞行服务					低空飞行服务
    低空经济	下游	低空运营服务	低空飞行服务	地面保障服务				地面保障服务
    低空经济	下游	低空运营服务	低空飞行服务	地面保障服务	飞行器检修			飞行器检修
    低空经济	下游	低空运营服务	低空飞行服务	地面保障服务	飞前定检			飞前定检
    低空经济	下游	低空运营服务	低空飞行服务	地面保障服务	加油服务			加油服务
    低空经济	下游	低空运营服务	低空飞行服务	地面保障服务	停机服务			停机服务
    低空经济	下游	低空运营服务	低空飞行服务	空中保障服务				空中保障服务
    低空经济	下游	低空运营服务	低空飞行服务	空中保障服务	飞行协助服务			飞行协助服务
    低空经济	下游	低空运营服务	低空飞行服务	空中保障服务	航空情报服务			航空情报服务
    低空经济	下游	低空运营服务	低空飞行服务	空中保障服务	航空气象服务			航空气象服务
    低空经济	下游	低空运营服务	低空飞行服务	空中保障服务	飞行计划服务			飞行计划服务
    低空经济	下游	低空运营服务	低空飞行服务	适航审定				适航审定
    低空经济	下游	低空运营服务	低空飞行服务	适航审定	航空燃料适航审定			航空燃料适航审定
    低空经济	下游	低空运营服务	低空飞行服务	适航审定	航空器适航审定			航空器适航审定
    低空经济	下游	低空飞行保障						低空飞行保障
    低空经济	下游	低空飞行保障	关键零件					关键零件
    低空经济	下游	低空飞行保障	检测检验服务					检测检验服务
    低空经济	下游	低空飞行保障	检测检验服务	无损检测				无损检测
    低空经济	下游	低空飞行保障	检测检验服务	可靠性评价				可靠性评价
    低空经济	下游	低空飞行保障	检测检验服务	产品疲劳检测				产品疲劳检测
    低空经济	下游	低空飞行保障	检测检验服务	复材构件成型检测				复材构件成型检测
    低空经济	下游	低空飞行保障	检测检验服务	机电性能测试系统				机电性能测试系统
    低空经济	下游	低空飞行保障	检测检验服务	电子膜厚检测				电子膜厚检测
    低空经济	下游	低空飞行保障	检测检验服务	电子缺陷检测				电子缺陷检测
    低空经济	下游	低空飞行保障	检测检验服务	电磁性能检测				电磁性能检测
    低空经济	下游	行业应用						行业应用
    低空经济	下游	行业应用	应急救援					应急救援
    低空经济	下游	行业应用	应急救援	森林灭火				森林灭火
    低空经济	下游	行业应用	应急救援	医疗救助				医疗救助
    低空经济	下游	行业应用	应急救援	抗洪救灾				抗洪救灾
    低空经济	下游	行业应用	应急救援	港口巡检				港口巡检
    低空经济	下游	行业应用	应急救援	电力巡检				电力巡检
    低空经济	下游	行业应用	应急救援	航拍测绘				航拍测绘
    低空经济	下游	行业应用	应急救援	航空护林				航空护林
    低空经济	下游	行业应用	应急救援	航空喷洒				航空喷洒
    低空经济	下游	行业应用	低空物流					低空物流
    低空经济	下游	行业应用	短途运输					短途运输
    低空经济	下游	行业应用	部件维修					部件维修
    低空经济	下游	行业应用	机体维修					机体维修
    低空经济	下游	行业应用	动力系统维修					动力系统维修
    低空经济	下游	行业应用	场地维修					场地维修
    低空经济	下游	行业应用	设备维护					设备维护
    低空经济	下游	行业应用	供电电设施维修					供电电设施维修
    低空经济	下游	飞行审批						飞行审批
    低空经济	下游	飞行审批	低空经济+物流					低空经济+物流
    低空经济	下游	飞行审批	低空经济+农业					低空经济+农业
    低空经济	下游	空域管理						空域管理
    低空经济	下游	空域管理	低空经济+旅游					低空经济+旅游
    低空经济	下游	空域管理	低空经济+消防					低空经济+消防


    
    """

    
    answer = llm_service.answer_question(MessageConverter.convert_messages([UserMessage(prompt)]),model)
    think = extract_think(answer)
    think_end_pos = answer.find('</think>')
    if think_end_pos != -1:
        # 返回</think>之前的所有内容
        no_think = answer[think_end_pos+8:].strip()
        no_think_st_pos = no_think.find('[')
        if no_think_st_pos != -1:
            no_think_end_pos = no_think.find(']')
            re  = no_think[no_think_st_pos:no_think_end_pos+1].strip()
            if re:
                re_lst = json.loads(re)
                return re_lst, think
            else:
                return [], think
        else:
            no_think_st_pos = no_think.find('{')
            if no_think_st_pos != -1:
                no_think_end_pos = no_think.find('}')
                re = no_think[no_think_st_pos:no_think_end_pos + 1].strip()
                if re:
                    re_lst = json.loads(re)
                    return re_lst, answer
                else:
                    return [], answer
            return [], answer


def extract_product_llm_two(industry, content):
    llm_service = Llm_Service()
    model = "DeepSeek-R1-Distill-Qwen-32B"
    messages = []
    
    # content = "\n\n\n\n".join(content)


    # prompt = f"""
    #     你是一位银行对公部门的产业研究专家，现正在分析低空经济产业链，请根据现有附件中的年报内容，补充低空经济/evtol/直升机/无人机产业链下每个末级环节点的产品/商品关键词列
    #     #产品/商品关键词列说明：指每个有内容的末级产业链环节点，当前市场上企业生产或销售该环节实体涉及的具体产品或商品名称都有哪些？当前市场上发布或企业生产该环节产品/商品涉及的技术都有哪些？
    #     #技能：
    #     1、请针对附件及以上说明，补充产业链所在行每个有内容的末级产业链环节点的产品/商品关键词列，内容尽可能穷尽，支持多个名称或关键字 
    #     2、产品/商品关键词、技术关键词必须提供来源依据，来源可靠客追溯（关键词所在段落）
    #     3、在年报中<第三节 管理层讨论与分析>章节，找到对应的产品/商品/业务关键词
    #     4、用中文思考，中文输出；
    #     """
    prompt = f"""    
    # 角色
    你是一个高效的产业链对应的产品/商品/业务关键词分类助手，能够准确地对产品/商品/业务关键词进行所属产业链环节分类，以 json 格式输出。
    #产品/商品关键词列说明：指每个有内容的末级产业链环节点，当前市场上企业生产或销售该环节实体涉及的具体产品或商品名称都有哪些？当前市场上发布或企业生产该环节产品/商品涉及的技术都有哪些？

    ## 技能
    ### 技能 1: 将给定的产品/商品/业务关键词按照给定产业链环节列表：{industry}进行分类
    1. 仔细分析给定内容，给定的是一个产业链环节，请你对其中的产业链环节在【文本内容】精准地找出每个给定产业链环节中对应的产品名称,如果有多个产品，请用逗号分隔,内容尽可能穷尽，支持产业链环节下多个名称或关键字 。
    2. 将每一个产业链环节及分类的产品/商品/业务关键词内容以 json 呈现，将所有json整合到一个列表中。
    3. json列表中，产业链环节一定要来源于{industry}，产品/商品/业务关键词可以在【产品/商品/业务关键词】中找到，如果找不到，则返回空字符串。
    4、用中文思考，中文输出；
    【产品/商品/业务关键词】
    {content}

    ===回复示例===
    [{{"产业链环节": "产业链环节1", "产品名称": "产品名称1,产品名称2"}},{{"产业链环节": "产业链环节2", "产品名称": "产品名称1,产品名称2"}}]
    ===示例结束===

    ## 限制
    - 只专注于从给定的【产品/商品/业务关键词】中抽取产业链环节{industry}所相关的产品，不考虑其他无关内容。
    - 如果不存在完全匹配的字段名称，则匹配与其最相似的字段。
    - 严格按照要求的格式输出 json，确保格式正确、完整，严禁输出任何其它无关内容,输出的json中的产业链环节一定要是{industry}中存在的产业链环节。

    
    """

    
    answer = llm_service.answer_question(MessageConverter.convert_messages([UserMessage(prompt)]),
                                                model)
    think = extract_think(answer)
    think_end_pos = answer.find('</think>')
    if think_end_pos != -1:
        # 返回</think>之前的所有内容
        no_think = answer[think_end_pos + 8:].strip()
        no_think_st_pos = no_think.find('[')
        if no_think_st_pos != -1:
            no_think_end_pos = no_think.find(']')
            re = no_think[no_think_st_pos:no_think_end_pos + 1].strip()
            if re:
                re_lst = json.loads(re)
                return re_lst, think
            else:
                return [], think
        else:
            return [], think

def process_result(content):
    pattern = r'\[(.*)\]'
    match = re.search(pattern, content, re.DOTALL)
    if match:
        # 提取 JSON 字符串
        json_str = '[' + match.group(1) + ']'
        
        # 解析 JSON
        llm_data = json.loads(json_str)
    else:
        llm_data = []
 
    return llm_data

def extract_think(text):
    """
    从文本中提取</think>标签之前的内容
    
    Args:
        text (str): 包含</think>标签的文本
        
    Returns:
        str: </think>之前的内容，如果没有找到标签则返回None
    """
    # 查找</think>的位置
    think_end_pos = text.find('</think>')
    
    if think_end_pos != -1:
        # 返回</think>之前的所有内容
        return text[:think_end_pos].strip()
    else:
        return text



def flatten_and_join(lst):
    """
    将包含字符串和逗号分隔字符串的列表展平并连接成一个字符串

    参数:
        lst (list): 输入列表，如 ["a", "c,d"]

    返回:
        str: 以逗号分隔的字符串，如 "a,b,c,"
    """
    # 初始化一个空列表用于存储展开后的元素
    expanded_elements = []

    # 遍历输入列表中的每一个元素
    for element in lst:
        if isinstance(element, str):
            # 如果元素中包含逗号，则拆分成多个元素
            expanded_elements.extend(element.split(','))
        else:
            # 如果不是字符串类型（例如可能是数字或其他类型），直接添加
            expanded_elements.append(str(element))

    # 合并所有元素为一个以逗号分隔的字符串，并在末尾加上一个逗号
    result_str = ','.join(expanded_elements)

    return result_str

def flatten_and_join1(lst):
    """
    将包含字符串和逗号分隔字符串的列表展平并连接成一个字符串

    参数:
        lst (list): 输入列表，如 ["a", "c,d"]

    返回:
        str: 以逗号分隔的字符串，如 "a,b,c,"
    """
    # 初始化一个空列表用于存储展开后的元素
    expanded_elements = []

    # 遍历输入列表中的每一个元素
    for element in lst:
        if isinstance(element, str):
            # 如果元素中包含逗号，则拆分成多个元素
            expanded_elements.extend(element.split(','))
        else:
            # 如果不是字符串类型（例如可能是数字或其他类型），直接添加
            expanded_elements.append(str(element).replace('{', '').replace('}', '').replace("'",''))

    # 合并所有元素为一个以逗号分隔的字符串，并在末尾加上一个逗号
    result_str = '|\n'.join(expanded_elements)

    return result_str

def flatten_and_join2(lst):
    """
    将包含字符串和逗号分隔字符串的列表展平并连接成一个字符串

    参数:
        lst (list): 输入列表，如 ["a", "c,d"]

    返回:
        str: 以逗号分隔的字符串，如 "a,b,c,"
    """
    # 初始化一个空列表用于存储展开后的元素
    expanded_elements = []

    # 遍历输入列表中的每一个元素
    for element in lst:
        if isinstance(element, str):
            # 如果元素中包含逗号，则拆分成多个元素
            expanded_elements.extend(element.split(','))
        else:
            # 如果不是字符串类型（例如可能是数字或其他类型），直接添加
            expanded_elements.append(str(element).replace('{', '').replace('}', '').replace("'",''))

    # 合并所有元素为一个以逗号分隔的字符串，并在末尾加上一个逗号
    result_str = '~\n'.join(expanded_elements)

    return result_str

import pandas as pd







def think_part(answer):
    result = ""
    answer = answer.replace('\n','')
    think_end_pos = answer.find('</think>')
    if think_end_pos != -1:

        # 返回</think>之前的所有内容
        result = answer[:think_end_pos + 8].strip()

    return result


def write_data_to_excel_sheet1(sheet_name,file_path,patent_data,patent_data1,think_final_data):
    """
    将字典中的产业链环节和产品名称插入到Excel文件指定sheet的L列和M列（从第5行开始）

    参数:
        file_path (str): Excel文件路径
        sheet_name (str): 要写入的工作表名称
        data_dict (dict): 包含数据的字典，格式如示例所示
    """

    # 提取要写入的数据列表（假设只处理第一个键）

    skip = []
    new_final_result = {}
    with pd.ExcelWriter(file_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as excel_writer:
        # book = excel_writer.book
        # if sheet_name in book.sheetnames:
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
        for (key, item),(key1, item1) in zip(patent_data.items(),patent_data1.items()):
            if key in skip:
                continue
            if item1 == {}:
                continue
            product_list = []
            company_list = []
            text_list = []
            code_list = []
            title_list = []
            item.pop('末级环节')
            think_list = []
            for k,i in item.items():
                if i == []:
                    continue
                code_list1 = []
                text_list1 = []
                title_list1 = []
                company_list1 = []
                product_list.append(k)
                try:

                    think_list.append(think_part(think_final_data[key][k]))
                except KeyError:
                    think_list.append("KEYERROR")
                select_code = []
                try:
                    i1 = item1[k]
                except KeyError:
                    i1 = i

                if isinstance(i1, list):
                    for j1 in i1:
                        if isinstance(j1, dict):
                            select_code.append(j1['公开公告号'])
                        elif isinstance(j1, list):
                            select_code = j1
                else:
                    select_code.append(i1['公开公告号'])
                for j in i:
                    if j['公开公告号'] not in select_code:
                        # print('过滤：'+j['公开公告号'])
                        continue
                    company_list1.append(j['申请人'])
                    code_list1.append(j['公开公告号'])
                    text_list1.append(j['摘要'])
                    title_list1.append(j['标题'])
                    company_str1 = flatten_and_join1(company_list1)
                    text_str1 = flatten_and_join1(text_list1)
                    code_str1 = flatten_and_join1(code_list1)
                    title_str1 = flatten_and_join1(title_list1)
                company_list.append(company_str1)
                code_list.append(code_str1)
                text_list.append(text_str1)
                title_list.append(title_str1)
            line_num = int(key)+1
            product_str = flatten_and_join2(product_list)
            company_str = flatten_and_join2(company_list)
            text_str = flatten_and_join2(text_list)
            code_str = flatten_and_join2(code_list)
            title_str = flatten_and_join2(title_list)
            think_str =flatten_and_join2(think_list)
            insert_data = []
            insert_data.append([product_str, company_str, text_str, code_str,think_str,'专利',title_str])
            df_insert = pd.DataFrame(insert_data)
            df.iloc[line_num, 9] = df_insert.iloc[0, 0]#产品名称
            df.iloc[line_num, 10] = df_insert.iloc[0, 1]#公司名称
            # df.iloc[line_num, 11] = df_insert.iloc[0, 2]#摘要
            # df.iloc[line_num, 12] = df_insert.iloc[0, 4]#推理
            df.iloc[line_num, 14] = df_insert.iloc[0, 5]#数据来源类型
            df.iloc[line_num, 15] = df_insert.iloc[0, 3]#专利号
            df.iloc[line_num, 16] = df_insert.iloc[0, 6]#标题
            df.to_excel(excel_writer, sheet_name=sheet_name, index=False, header=False)
            print(f"{key}数据已成功写入")







    # 写回整个sheet

def write_data_to_excel_sheet_new(sheet_name,file_path,patent_data):
    """
    将字典中的产业链环节和产品名称插入到Excel文件指定sheet的L列和M列（从第5行开始）

    参数:
        file_path (str): Excel文件路径
        sheet_name (str): 要写入的工作表名称
        data_dict (dict): 包含数据的字典，格式如示例所示
    """


    skip = []
    new_final_result = {}
    with pd.ExcelWriter(file_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as excel_writer:

        df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
        for key, item in patent_data.items():
            if key in skip:
                continue
            if item == {}:
                continue
            product_list = []
            company_list = []
            text_list = []
            code_list = []
            title_list = []
            think_list = []
            for k,i in item.items():
                if i == []:
                    continue
                if i == [{}]:
                    continue
                code_list1 = []
                text_list1 = []
                title_list1 = []
                company_list1 = []
                product_list.append(k)
                # try:
                #
                #     think_list.append(think_final_data[key][k])
                # except KeyError:
                #     think_list.append("KEYERROR")

                for j in i:
                    try:
                        if isinstance(j, dict):
                            patent_code=j['申请号']
                            think_code=j['推理过程']
                            text_code=j['原文依据']

                        elif isinstance(j, str):
                            print('str')
                            patent_code = j
                    except KeyError:
                        print(k)
                        print(j)
                        continue
                    # res = query_company_by_name(db, patent_code)
                    # data = SQLUtil.model_to_dict(res)
                    # company_list1.append(data["ApplicantAndPatentee"])
                    # code_list1.append(data["GrantPublicationNumber"])
                    text_list1.append(text_code)
                    title_list1.append(think_code)
                    code_list1.append(patent_code)
                    # company_str1 = flatten_and_join1(company_list1)
                    text_str1 = flatten_and_join1(text_list1)
                    code_str1 = flatten_and_join1(code_list1)
                    title_str1 = flatten_and_join1(title_list1)
                # company_list.append(company_str1)
                code_list.append(code_str1)
                text_list.append(text_str1)
                title_list.append(title_str1)
            line_num = int(key)+1
            product_str = flatten_and_join2(product_list)
            company_str = flatten_and_join2(company_list)
            text_str = flatten_and_join2(text_list)
            code_str = flatten_and_join2(code_list)
            title_str = flatten_and_join2(title_list)
            think_str =flatten_and_join2(think_list)
            insert_data = []
            insert_data.append([product_str, company_str, text_str, code_str,think_str,'专利',title_str])
            df_insert = pd.DataFrame(insert_data)
            df.iloc[line_num, 9] = df_insert.iloc[0, 0]#产品名称
            # df.iloc[line_num, 10] = df_insert.iloc[0, 1]#公司名称
            # df.iloc[line_num, 11] = df_insert.iloc[0, 2]#摘要
            # df.iloc[line_num, 12] = df_insert.iloc[0, 6]#推理
            # df.iloc[line_num, 14] = df_insert.iloc[0, 5]#数据来源类型
            df.iloc[line_num, 15] = df_insert.iloc[0, 3]#专利号
            # df.iloc[line_num, 16] = df_insert.iloc[0, 6]#标题
            df.to_excel(excel_writer, sheet_name=sheet_name, index=False, header=False)
            # print(f"{key}数据已成功写入")




def write_data_to_excel_sheet_sql(sheet_name,file_path):

    # 提取要写入的数据列表（假设只处理第一个键）
    SQLUtil.connect()
    db = SQLUtil.get_session()

    df = pd.read_excel(file_path, sheet_name=sheet_name)

    # 假设申请号列名为'申请号'，详情列名为'详情'（根据实际情况修改）
    application_number_column = '申请号'
    company_column = '公司名称'
    new_company_column = '公司名称'
    text_column = '公司产业链环节&产品/商品/业务所在文本段落（标题，摘要，文本块等）'
    title_column = '公司数据来源标题'
    details_column = '公司数据来源类型'


    # 遍历申请号列，查询数据库并更新详情列
    for index, row in df.iterrows():
        application_number = row[application_number_column]

        # 跳过空的申请号
        if pd.isna(application_number):
            continue

        try:
            # 查询数据库（假设使用申请号查询专利详情）
            patent_info = query_company_by_name(db, application_number)


            # 如果查询到结果，更新详情列
            if patent_info:
                # 将查询结果转换为字典（根据实际模型属性调整）
                data = SQLUtil.model_to_dict(patent_info)
                # 将字典转换为JSON字符串或其他格式
                df.at[index, company_column] = json.dumps(data["ApplicantAndPatentee"], ensure_ascii=False)
                df.at[index, new_company_column] = json.dumps(data["ApplicantAndPatentee"], ensure_ascii=False)
                # df.at[index, text_column] = json.dumps(data["AbstractText"], ensure_ascii=False)
                df.at[index, title_column] = json.dumps(data["PatentTitle"], ensure_ascii=False)
                df.at[index, details_column] = '专利'
            else:
                df.at[index, details_column] = '专利'

        except Exception as e:
            print(f"处理申请号 {application_number} 时出错: {str(e)}")
            df.at[index, details_column] = f"查询出错: {str(e)}"

    # 将更新后的数据写回Excel
    with pd.ExcelWriter(file_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as excel_writer:
        df.to_excel(excel_writer, sheet_name=sheet_name, index=False)

    # 关闭数据库连接
    db.close()




def write_data_to_excel_sheet_think(sheet_name,file_path,result_dict):


    df = pd.read_excel(file_path, sheet_name=sheet_name)

    # 3. 定义目标列名（根据实际Excel表头调整）
    product_column = '匹配产品/环节'  # 用于匹配的列
    think_column = '大模型推理过程（为什么该末级环节或者产品属于该公司的思考过程）'
    text_column = '公司产业链环节&产品/商品/业务所在文本段落（标题，摘要，文本块等）'# 要写入的列

    # 4. 遍历Excel行，匹配并写入数据
    for index, row in df.iterrows():
        # 获取当前行的"匹配产品/环节"值
        product_value = row[product_column]
        num_value = row['申请号']
        chain_value = row['末级环节']

        # 跳过空值或不匹配的值
        if pd.isna(product_value):
            continue  # 不匹配则保持空值
        product_name =chain_value +'|'+ product_value
        # 从result_dict中获取对应的推理过程，转换为字符串写入
        try:
            think_data_list = result_dict[product_name]

            for think_data in think_data_list:
                try:
                    if num_value == think_data['申请号']:
                        df.at[index, think_column] = str(think_data['推理过程'])
                        df.at[index, text_column] = str(think_data['原文依据'])
                except Exception as e:
                    continue
        except Exception as e:
            continue

    # 5. 将更新后的数据写回Excel，替换原工作表
    with pd.ExcelWriter(
            file_path,
            engine='openpyxl',
            mode='a',
            if_sheet_exists='replace'
    ) as excel_writer:
        df.to_excel(excel_writer, sheet_name=sheet_name, index=False)







    # 写回整个sheet
def split_list(lst):
    """将列表按每50个元素分割成多个子列表"""
    if len(lst) <= 50:  # 列表长度不超过50，无需分割
        return [lst]
    print('开始分割专利列表')
    result = []
    for i in range(0, len(lst), 50):
        result.append(lst[i:i + 50])  # 每次取50个元素
    return result
async def llm_patent_process(industry,path,llm_service):
    import time
    output_json_file = f"{path}/patent_product_dict_jieba_content_new.json"
    skip_list = ["72"]
    skip_list1 =["电池"]
    product_final_data = {}
    think_final_data = {}
    max_retries = 5
    retry_delay = 1
    with open(output_json_file, "r", encoding="utf-8") as f:
        data = json.load(f)
        if isinstance(data, dict):
            for key, value in data.items():
                chain = value['末级环节']
                product_data = {}
                think_data = {}
                value.pop('末级环节')
                # if key not in skip_list:
                #     continue
                for item_key,item in tqdm(value.items()):
                    # if item_key not in skip_list1:
                    #     continue
                    if item ==[]:
                        continue

                    # for i in item:
                    #     # i.pop('摘要')
                    #     pantent_item = []
                    #     pantent_item.append(i)
                    chunks = split_list(item)
                    llm_result = []
                    llm_think = []
                    print(item_key)
                    for chunk in tqdm(item):

                        for attempt in range(max_retries):
                            try:
                                re,think =extract_product_llm_one(item_key,chunk,llm_service,industry,chain=chain)
                                llm_result = llm_result + re
                                llm_think.append(think)
                                break
                            except Exception as e:
                                # print(e)
                                print(f'error:key:{key}')
                                print(f'error:item_key:{item_key}')
                                if attempt < max_retries - 1:  # 不是最后一次尝试
                                    print(f"将在 {retry_delay} 秒后重试...")
                                    time.sleep(retry_delay)  # 等待指定时间后重试
                                else:
                                    print("已达到最大重试次数，跳过当前项")
                                    continue

                    product_data[item_key] = llm_result
                    think_data[item_key] = llm_think
                product_final_data[key] = product_data
                think_final_data[key] = think_data
                output_json_file = f"{path}/result.json"
                with open(output_json_file, "w", encoding="utf-8") as json_file:
                    json.dump(product_final_data, json_file, indent=4, ensure_ascii=False)
                output_json_file = f"{path}/result_think.json"
                with open(output_json_file, "w", encoding="utf-8") as json_file:
                    json.dump(think_final_data, json_file, indent=4, ensure_ascii=False)
                print(f'{key}:ok')

async def file_join():
    df = read_excel_file("C:/Users/<USER>/Desktop/广东新能源汽车/广东新能源汽车-广东建行POC.xlsx",sheet_name = "01新能源汽车-产业链结构",columns_to_read = ["末级环节", "产品/商品/业务（专利）"])

    df['产品/商品/业务（专利）'] = df['产品/商品/业务（专利）'].astype(str)
    df['末级环节'] = df['末级环节'].astype(str)

    # 拼接两列内容
    df['产品/商品/业务（专利）'] = df.apply(
        lambda row: ','.join(filter(None, [row['产品/商品/业务（专利）'], row['末级环节']])),
        axis=1
    )

    # 保存回原文件或新文件
    df.to_excel("C:/Users/<USER>/Desktop/广东新能源汽车/广东新能源汽车-广东建行POC_chain.xlsx", index=False)

def extract_keywords(keywords_set):
    words = jieba.lcut(keywords_set)
    # 将标题分词后形成的词语集合
    title_words = list(set(words))
    # 检查是否包含 keywords_set 中的所有关键词

    return title_words

def build_pattern(items):
    patterns = ""
    for item in items:
        pattern = f'(?=.*{item})'
        patterns += pattern
    return patterns

async def build_chain_product():
    title = "年报"
    #读取excel文件特定位置
    data1 = read_excel_file("C:/Users/<USER>/Desktop/广东低空经济专利/著录项-20250702144741.xlsx",sheet_name = "专利数据"  , columns_to_read = ["公开(公告)号", "标题","摘要","申请人"])
    data2 = read_excel_file("C:/Users/<USER>/Desktop/广东低空经济专利/著录项-20250702150455.xlsx",sheet_name = "专利数据"  , columns_to_read = ["公开(公告)号", "标题","摘要","申请人"])
    data3 = read_excel_file("C:/Users/<USER>/Desktop/广东低空经济专利/著录项-20250702151355.xlsx",sheet_name = "专利数据"  , columns_to_read = ["公开(公告)号", "标题","摘要","申请人"])
    data4 = read_excel_file("C:/Users/<USER>/Desktop/广东新能源汽车/广东新能源汽车-广东建行POC.xlsx",sheet_name = "01新能源汽车-产业链结构"  , columns_to_read = ["产品/商品/业务（专利）"])
    data5 = read_excel_file("C:/Users/<USER>/Desktop/广东新能源汽车/广东新能源汽车专利(1).xlsx",sheet_name = "Sheet1"  , columns_to_read = ["公开(公告)号", "标题","摘要","申请人"])
    data = pd.concat([data5])
    filtered_df = data4.dropna().reset_index()
    product_list = filtered_df["产品/商品/业务（专利）"].tolist()
    product_list_row = filtered_df["index"].tolist()


    product_final_data = {}
    for index,i in enumerate(product_list):
        product_dict = {}
        product = i.replace('，',',').split(',')
        product = list(set(product))
        product_filtered_list = [item for item in product if item != '']
        line = product_list_row[index]
        for item in product_filtered_list:
            filtered_data = data[data['标题'].str.contains(item, na=False)]

            # 创建一个新的列表，按照指定格式存储信息
            result = [
                {
                    '标题': row['标题'],
                    '摘要': row['摘要'],
                    '申请人': row['申请人'],
                    '公开公告号': row['公开(公告)号']
                }
                for _, row in filtered_data.iterrows()
            ]
            product_dict[item] = result
        product_final_data[line] = product_dict
    # print(product_final_data)
    output_json_file = "C:/Users/<USER>/Desktop/广东新能源汽车/patent_product_dict.json"
    with open(output_json_file, "w", encoding="utf-8") as json_file:
        json.dump(product_final_data, json_file, indent=4, ensure_ascii=False)

async def combine_data(path,industry,key_col="末级环节", value_col="产品/商品/业务（专利）"):
    df = read_excel_file(f"{path}/广东{industry}-广东建行POC.xlsx",sheet_name = f"02{industry}-补充产品词"  , columns_to_read = [key_col,value_col])
    # df1 = read_excel_file(f"{path}/广东{industry}-广东建行POC.xlsx", sheet_name=f"01{industry}-产业链结构", columns_to_read=[key_col])
    industry_dict = {}
    for _, row in df.iterrows():
        chain = row[key_col]
        product = row[value_col]

        if chain in industry_dict:
            # 如果键已存在，将产品添加到列表中
            if isinstance(industry_dict[chain], list):
                industry_dict[chain].append(product)
            else:
                industry_dict[chain] = [industry_dict[chain], product]
        else:
            # 如果键不存在，直接设置产品
            industry_dict[chain] = [chain, product]
    output_json_file = f"{path}/combine_data.json"
    with open(output_json_file, "w", encoding="utf-8") as json_file:
        json.dump(industry_dict, json_file, indent=4, ensure_ascii=False)

async def get_number(path):
    output_json_file = f"{path}/patent_number.json"
    #读取excel文件特定位置
    data1 = read_excel_file(f"{path}/著录项-20250702144741.xlsx",sheet_name = "专利数据"  , columns_to_read = ["公开(公告)号", "申请号"])
    data2 = read_excel_file(f"{path}/著录项-20250702150455.xlsx",sheet_name = "专利数据"  , columns_to_read = ["公开(公告)号", "申请号"])
    data3 = read_excel_file(f"{path}/著录项-20250702151355.xlsx",sheet_name = "专利数据"  , columns_to_read = ["公开(公告)号", "申请号"])
    data = pd.concat([data1,data2,data3])
    patent_dict = {}
    for _, row in data.iterrows():
        p_id = row["公开(公告)号"]
        p_id2 = row["申请号"]

        # 跳过公开号为空的记录
        if pd.isna(p_id):
            continue

        # 处理重复的公开号，将申请号存储为列表
        if p_id in patent_dict:
            if not isinstance(patent_dict[p_id], list):
                patent_dict[p_id] = [patent_dict[p_id]]  # 将已有值转换为列表
            patent_dict[p_id].append(p_id2)
        else:
            patent_dict[p_id] = p_id2
    with open(output_json_file, "w", encoding="utf-8") as json_file:
        json.dump(patent_dict, json_file, indent=4, ensure_ascii=False)

async def build_chain_product_jieba(path,industry):
    output_json_file = f"{path}/combine_data.json"
    with open(output_json_file, "r", encoding="utf-8") as f:
        combine_data = json.load(f)

    #读取excel文件特定位置
    data1 = read_excel_file(f"{path}/著录项-20250702144741.xlsx",sheet_name = "专利数据"  , columns_to_read = ["申请号", "标题","摘要","申请人"])
    data2 = read_excel_file(f"{path}/著录项-20250702150455.xlsx",sheet_name = "专利数据"  , columns_to_read = ["申请号", "标题","摘要","申请人"])
    data3 = read_excel_file(f"{path}/著录项-20250702151355.xlsx",sheet_name = "专利数据"  , columns_to_read = ["申请号", "标题","摘要","申请人"])
    data4 = read_excel_file(f"{path}/广东{industry}-广东建行POC.xlsx",sheet_name = f"03{industry}-中心客群"  , columns_to_read = ["末级环节"])
    # data5 = read_excel_file(f"{path}/广东{industry}专利(1).xlsx",sheet_name = "Sheet1"  , columns_to_read = ["公开(公告)号", "标题","摘要","申请人"])
    data = pd.concat([data1,data2,data3])

    filtered_df = data4.dropna().reset_index()
    # product_list = filtered_df["产品/商品/业务（专利）"].tolist()
    chain_list = filtered_df["末级环节"].tolist()
    product_list_row = filtered_df["index"].tolist()


    product_final_data = {}
    for index,i in enumerate(chain_list):
        try:
            product = list(set(combine_data[i]))
        except KeyError:
            product = [i]
        keywords_dic = {}
        product_dict = {}
        for item in product:
            if re.search(r'[()*+?^$\\.|]', item):
                keywords_dic[item] = 'None'
            else:
                keywords_dic[item] = extract_keywords(item)

        # product = list(set(product))
        product_filtered_list = [item for item in product if item != '']
        line = product_list_row[index]
        for item in product_filtered_list:
            filtered_data = data[data['标题'].str.contains(item, na=False)]
            first_part = data['摘要'].str.split('，').str[0]
            filtered_data1 = data[first_part.str.contains(item, na=False)]
            pattern = build_pattern(keywords_dic[item])
            print(pattern)
            filtered_data2 = data[data['标题'].str.contains(pattern, regex=True,na=False)]

            # 创建一个新的列表，按照指定格式存储信息
            result = [
                {
                    '标题': row['标题'],
                    '摘要': row['摘要'],
                    '申请人': row['申请人'],
                    '申请号': row['申请号']
                }
                for _, row in filtered_data.iterrows()
            ]
            result1 = [
                {
                    '标题': row['标题'],
                    '摘要': row['摘要'],
                    '申请人': row['申请人'],
                    '申请号': row['申请号']
                }
                for _, row in filtered_data1.iterrows()
            ]
            result2 = [
                {
                    '标题': row['标题'],
                    '摘要': row['摘要'],
                    '申请人': row['申请人'],
                    '申请号': row['申请号']
                }
                for _, row in filtered_data2.iterrows()
            ]
            result_all =result+result1+result2
            if len(result_all) < 1000:
                # 去重
                unique = set(json.dumps(d, sort_keys=True) for d in result_all)
                unique_dicts = [json.loads(s) for s in unique]
                product_dict[item] = unique_dicts
            else:
                product_dict[item] = []
            product_dict['末级环节'] = chain_list[index]
        product_final_data[line] = product_dict
    # print(product_final_data)
    output_json_file = f"{path}/patent_product_dict_jieba_content_new.json"
    with open(output_json_file, "w", encoding="utf-8") as json_file:
        json.dump(product_final_data, json_file, indent=4, ensure_ascii=False)

async def build_chain_product_jieba_inclued_chain(path,industry):
    output_json_file = f"{path}/combine_data.json"
    with open(output_json_file, "r", encoding="utf-8") as f:
        combine_data = json.load(f)

    #读取excel文件特定位置
    data1 = read_excel_file(f"{path}/著录项-20250702144741.xlsx",sheet_name = "专利数据"  , columns_to_read = ["申请号", "标题","摘要","申请人"])
    data2 = read_excel_file(f"{path}/著录项-20250702150455.xlsx",sheet_name = "专利数据"  , columns_to_read = ["申请号", "标题","摘要","申请人"])
    data3 = read_excel_file(f"{path}/著录项-20250702151355.xlsx",sheet_name = "专利数据"  , columns_to_read = ["申请号", "标题","摘要","申请人"])
    data4 = pd.read_excel(f"{path}/广东{industry}-广东建行POC.xlsx",sheet_name = f"01{industry}-产业链结构")
    # data5 = read_excel_file(f"{path}/广东{industry}专利(1).xlsx",sheet_name = "Sheet1"  , columns_to_read = ["公开(公告)号", "标题","摘要","申请人"])
    data = pd.concat([data1,data2,data3])


    product_final_data = {}
    for idx, row in data4.iterrows():
        chain_type = '' if pd.isna(row['产业类型']) else row['产业类型']
        chain_stream = '' if pd.isna(row["上中下游"]) else row["上中下游"]
        one_chain = '' if pd.isna(row['一级产业链环节']) else row['一级产业链环节']
        two_chain = '' if pd.isna(row['二级产业链环节']) else row['二级产业链环节']
        three_chain = '' if pd.isna(row['三级产业链环节']) else row['三级产业链环节']
        four_chain = '' if pd.isna(row['四级产业链环节']) else row['四级产业链环节']
        five_chain = '' if pd.isna(row['五级产业链环节']) else row['五级产业链环节']
        six_chain = '' if pd.isna(row['六级产业链环节']) else row['六级产业链环节']
        end_chain = '' if pd.isna(row["末级环节"]) else row["末级环节"]
        if end_chain == '':
            product_final_data[idx] = {'产业类型':chain_type,'上中下游':chain_stream,'一级产业链环节':one_chain,'二级产业链环节':two_chain,'三级产业链环节':three_chain,'四级产业链环节':four_chain,'五级产业链环节':five_chain,'六级产业链环节':six_chain,'末级环节':end_chain}
            continue
        try:
            product = list(set(combine_data[end_chain]))
        except KeyError:
            product = [end_chain]
        keywords_dic = {}
        product_dict = {}
        for item in product:
            if re.search(r'[()*+?^$\\.|]', item):
                keywords_dic[item] = 'None'
            else:
                keywords_dic[item] = extract_keywords(item)

        # product = list(set(product))
        product_filtered_list = [item for item in product if item != '']

        for item in product_filtered_list:
            filtered_data = data[data['标题'].str.contains(item, na=False)]
            first_part = data['摘要'].str.split('，').str[0]
            filtered_data1 = data[first_part.str.contains(item, na=False)]
            pattern = build_pattern(keywords_dic[item])
            print(pattern)
            filtered_data2 = data[data['标题'].str.contains(pattern, regex=True,na=False)]

            # 创建一个新的列表，按照指定格式存储信息
            result = [
                {
                    '标题': row['标题'],
                    '摘要': row['摘要'],
                    '申请人': row['申请人'],
                    '申请号': row['申请号']
                }
                for _, row in filtered_data.iterrows()
            ]
            result1 = [
                {
                    '标题': row['标题'],
                    '摘要': row['摘要'],
                    '申请人': row['申请人'],
                    '申请号': row['申请号']
                }
                for _, row in filtered_data1.iterrows()
            ]
            result2 = [
                {
                    '标题': row['标题'],
                    '摘要': row['摘要'],
                    '申请人': row['申请人'],
                    '申请号': row['申请号']
                }
                for _, row in filtered_data2.iterrows()
            ]
            result_all =result+result1+result2
            # 去重
            unique = set(json.dumps(d, sort_keys=True) for d in result_all)
            unique_dicts = [json.loads(s) for s in unique]
            product_dict[item] = unique_dicts
            product_dict['末级环节'] = end_chain
            product_dict['产业类型'] = chain_type
            product_dict['上中下游'] = chain_stream
            product_dict['一级产业链环节'] = one_chain
            product_dict['二级产业链环节'] = two_chain
            product_dict['三级产业链环节'] = three_chain
            product_dict['四级产业链环节'] = four_chain
            product_dict['五级产业链环节'] = five_chain
            product_dict['六级产业链环节'] = six_chain

        product_final_data[idx] = product_dict

    output_json_file = f"{path}/patent_product_dict_jieba_content_new.json"
    with open(output_json_file, "w", encoding="utf-8") as json_file:
        json.dump(product_final_data, json_file, indent=4, ensure_ascii=False)
async def build_chain_data(path,industry):
    output_json_file = f"{path}/patent_product_dict_jieba_content_new.json"
    output_json_file1 = f"{path}/result.json"
    output_json_thinkfile = f"{path}/result_think.json"
    file_path =f"{path}/广东{industry}-广东建行POC.xlsx"
    sheet_name = f"03{industry}-中心客群"
    with open(output_json_file, "r", encoding="utf-8") as f:
        data = json.load(f)
        patent_data = data
    with open(output_json_file1, "r", encoding="utf-8") as f:
        data1 = json.load(f)
        patent_data1 = data1
    with open(output_json_thinkfile, "r", encoding="utf-8") as f:
        think = json.load(f)
        patent_think = think
    write_data_to_excel_sheet1(sheet_name,file_path, patent_data,patent_data1,patent_think)

async def build_chain_data_new(path,industry):
    # output_json_file = f"{path}/patent_product_dict_jieba_content_new.json"
    output_json_file1 = f"{path}/result.json"
    output_json_thinkfile = f"{path}/result_think.json"
    file_path =f"{path}/广东{industry}-广东建行POC.xlsx"
    sheet_name = f"03{industry}-中心客群"
    # with open(output_json_file, "r", encoding="utf-8") as f:
    #     data = json.load(f)
    #     patent_data = data
    with open(output_json_file1, "r", encoding="utf-8") as f:
        data = json.load(f)
        patent_data = data
    # with open(output_json_thinkfile, "r", encoding="utf-8") as f:
    #     think = json.load(f)
    #     patent_think = think
    write_data_to_excel_sheet_new(sheet_name,file_path, patent_data)

async def build_chain_data2(path,industry):
    # 读取 Excel 文件
    file_path = f'{path}/广东{industry}-广东建行POC.xlsx'
    file_path1 = f'{path}/广东{industry}-广东建行POCv1.xlsx' # 替换为你的文件路径
    sheet_name = f'03{industry}-中心客群'  # 替换为你的工作表名
    df = pd.read_excel(file_path, sheet_name=sheet_name)

    # 假设需要拆分的列为 '公司'
    expanded_data = []

    for index, row in df.iterrows():
        companies1 = str(row['匹配产品/环节']).split('~')
        # companies2 = str(row['原文公司全称']).split('~')
        # companies3 = str(row['大模型推理过程（为什么该末级环节或者产品属于该公司的思考过程）']).split('~')
        companies4 = str(row['申请号']).split('~')# 将公司字段按逗号分割
        # companies5 = str(row['公司数据来源标题']).split('~')
        for company1,company4 in zip(companies1,companies4):
            new_row = row.copy()  # 复制原行数据
            new_row['匹配产品/环节'] = company1
            # new_row['原文公司全称'] = company2
            # new_row['大模型推理过程（为什么该末级环节或者产品属于该公司的思考过程）'] = company3
            new_row['申请号'] = company4
            # new_row['公司数据来源标题'] = company5# 更新公司字段并去除可能的空格
 # 更新标题字段并去除可能的空格
            expanded_data.append(new_row)
    #创建一个新的 DataFrame
    df_expanded = pd.DataFrame(expanded_data)

    # 写回到 Excel 文件
    with pd.ExcelWriter(file_path1, engine='openpyxl', mode='w') as writer:
        df_expanded.to_excel(writer, sheet_name=sheet_name, index=False)


async def build_chain_data3(path,industry):
    file_path = f'{path}/广东{industry}-广东建行POCv1.xlsx'
    file_path1 = f'{path}/广东{industry}-广东建行POCv2.xlsx' # 替换为你的文件路径
    sheet_name = f'03{industry}-中心客群'  # 替换为你的工作表名
    df = pd.read_excel(file_path, sheet_name=sheet_name)

    # 假设需要拆分的列为 '公司'
    expanded_data = []

    for index, row in df.iterrows():

        # companies2 = str(row['原文公司全称']).split('|')
        # companies3 = str(row['摘要']).split('|')
        companies4 = str(row['申请号']).split('|')# 将公司字段按逗号分割
        # companies5 = str(row['公司数据来源标题']).split('|')
        for company4 in companies4:
            new_row = row.copy()  # 复制原行数据
            # new_row['原文公司全称'] = company2
            # new_row['摘要'] = company3
            new_row['申请号'] = company4
            # new_row['公司数据来源标题'] = company5# 更新公司字段并去除可能的空格
 # 更新标题字段并去除可能的空格
            expanded_data.append(new_row)
    #创建一个新的 DataFrame
    df_expanded = pd.DataFrame(expanded_data)

    # 写回到 Excel 文件
    with pd.ExcelWriter(file_path1, engine='openpyxl', mode='w') as writer:
        df_expanded.to_excel(writer, sheet_name=sheet_name, index=False)

async def build_chain_data_sql(path,industry):

    file_path =f"{path}/广东{industry}-广东建行POCv2.xlsx"
    sheet_name = f"03{industry}-中心客群"

    write_data_to_excel_sheet_sql(sheet_name,file_path)


async def build_chain_data_think(path,industry):
    output_json_think_file = f"{path}/think.json"
    file_path =f"{path}/广东{industry}-广东建行POCv3.xlsx"
    sheet_name = f"03{industry}-中心客群"

    with open(output_json_think_file, "r", encoding="utf-8") as f:
        think = json.load(f)
        patent_think = think
    write_data_to_excel_sheet_think(sheet_name,file_path, patent_think)

async def build_chain_data4(path,industry):
    file_path = f'{path}/广东{industry}-广东建行POCv2.xlsx'
    file_path1 = f'{path}/广东{industry}-广东建行POCv3.xlsx' # 替换为你的文件路径
    sheet_name = f'03{industry}-中心客群'  # 替换为你的工作表名
    df = pd.read_excel(file_path, sheet_name=sheet_name)

    # 假设需要拆分的列为 '公司'
    expanded_data = []

    for index, row in df.iterrows():
        if ',' in str(row['公司名称']):
            companies = str(row['公司名称']).split(',')
            for company in companies:
                new_row = row.copy()  # 复制原行数据
                new_row['公司名称'] = company  # 更新公司字段并去除可能的空格
                expanded_data.append(new_row)

        else:
            companies = str(row['公司名称']).split(';')
            for company in companies:
                new_row = row.copy()  # 复制原行数据
                new_row['公司名称'] = company  # 更新公司字段并去除可能的空格
                expanded_data.append(new_row)

    #创建一个新的 DataFrame
    df_expanded = pd.DataFrame(expanded_data)

    # 写回到 Excel 文件
    with pd.ExcelWriter(file_path1, engine='openpyxl', mode='w') as writer:
        df_expanded.to_excel(writer, sheet_name=sheet_name, index=False)


Base = declarative_base()


class PatentInfo(Base):
    """企业主信息表 SQLAlchemy ORM 模型"""

    __tablename__ = "PatentInfo"
    id = Column(Integer, primary_key=True, index=True,nullable=False)
    PatentId = Column(String(100), primary_key=True,nullable=False, comment="企业代码")
    PatentTitle = Column(String(100), nullable=True, comment="中文名称")
    PatentType = Column(String(100), nullable=False, comment="企业别称")
    CountryOfRegistration= Column(String(100), nullable=False, comment="企业别称")
    LegalStatus = Column(Text, nullable=False, comment="曾用名")
    LegalStatusdate = Column(DateTime, nullable=False, server_default="CURRENT_TIMESTAMP", comment="创建时间")
    CurrentLegalStatus = Column(String(200), nullable=False, comment="英文全称")
    CurrentLegalStatusdate = Column(DateTime, nullable=False, server_default="CURRENT_TIMESTAMP", comment="创建时间")
    FilingDate = Column(DateTime, nullable=False, server_default="CURRENT_TIMESTAMP", comment="创建时间")
    ApplicationNumber = Column(String(100), nullable=False, comment="股票简称")
    GrantPublicationNumber = Column(String(500), nullable=False, comment="品牌名称")
    GrantPublicationDate = Column(DateTime, nullable=False, server_default="CURRENT_TIMESTAMP", comment="创建时间")
    PriorityNumber = Column(String(100), nullable=False, comment="大模型简称")
    PriorityDate = Column(DateTime, nullable=False, server_default="CURRENT_TIMESTAMP", comment="创建时间")
    IPCClass = Column(Text, nullable=False, comment="其他简称")
    GBCClass = Column(String(255), nullable=False, comment="信用代码")
    CPCClass = Column(String(255), nullable=False, comment="信用代码")
    ApplicantAndPatentee = Column(Text, nullable=False, comment="其他简称")
    PreviousApplicants = Column(Text, nullable=False, comment="其他简称")
    Inventors = Column(Text, nullable=False, comment="其他简称")
    Address = Column(String(255), nullable=False, comment="信用代码")
    PostalCode = Column(String(255), nullable=False, comment="信用代码")
    PatentAgency = Column(String(255), nullable=False, comment="信用代码")
    PatentAgent = Column(String(255), nullable=False, comment="信用代码")
    ApplicationStage = Column(String(255), nullable=False, comment="信用代码")
    AbstractText = Column(Text, nullable=False, comment="其他简称")
    DrawingsPath = Column(Text, nullable=False, comment="其他简称")
    ClaimsText = Column(Text, nullable=False, comment="其他简称")
    SpecificationText = Column(Text, nullable=False, comment="其他简称")
    OriginalDocument = Column(Text, nullable=False, comment="其他简称")

def query_company_by_name(db: Session, patent_num:str):
    res = db.query(PatentInfo).filter(PatentInfo.GrantPublicationNumber == patent_num).first()
    return res


async def get_think(path):
    input_json_file = f"{path}/result.json"
    with open(input_json_file, "r", encoding="utf-8") as f:
        data = json.load(f)
    input_json_file1 = f"{path}/patent_product_dict_jieba_content_new.json"
    with open(input_json_file1, "r", encoding="utf-8") as f:
        data1 = json.load(f)
    output_json_file = f"{path}/think.json"
    #读取excel文件特定位置
    result_dict = {}
    # 遍历原始字典的每个层级
    for outer_key, inner_dict in data.items():
        data_type = data1[outer_key]['末级环节']
        # 遍历内层字典，直接提取产业链节点和对应的值
        for industry_node, values in inner_dict.items():
            # if industry_node =='无人机':
            #     continue
            # 将产业链节点和其值添加到结果字典中
            node_name = data_type+'|'+industry_node
            result_dict[node_name] = values
    with open(output_json_file, "w", encoding="utf-8") as json_file:
        json.dump(result_dict, json_file, indent=4, ensure_ascii=False)

if __name__ == "__main__":
    from utils.sql_util import SQLUtil
    db = SQLUtil.get_session()
    industry = '低空经济'
    path =f'C:/Users/<USER>/Desktop/广东{industry}'
    llm_service = Llm_Service()
    #公告号和申请号匹配 弃用
    # asyncio.run(get_think(path))
    # #产业链末级环节和产品组合
    # asyncio.run(combine_data(path,industry))
    # #分词专利搜索
    # asyncio.run(build_chain_product_jieba(path,industry))
    # #模型判断
    # asyncio.run(llm_patent_process(industry,path,llm_service))
    # #申请号写入表格
    # asyncio.run(build_chain_data_new(path,industry))
    # #申请号分割
    # asyncio.run(build_chain_data2(path,industry))
    # asyncio.run(build_chain_data3(path,industry))
    # #读取sql数据
    # asyncio.run(build_chain_data_sql(path,industry))
    # #分割公司字段
    # asyncio.run(build_chain_data4(path,industry))
    # #编排写入think数据
    # asyncio.run(get_think(path))
    # asyncio.run(build_chain_data_think(path,industry))



