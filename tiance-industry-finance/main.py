#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File         :main.py
@Description  :
<AUTHOR>
@Date         :2025/02/27 16:16:42
"""

from utils.sql_util import SQLUtil
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from configs.api_config import ApiConfig
from api.routes import (
    ai_assistant, common_extension,
    company_summary, download_document,
    financial_suggestions, get_key_companies,
    industry_chain_map, industry_evaluation,
    key_companies, node_analysis,
    node_companies, product_extension,
    push_company_info, related_companies,
    research_report_extension, search_document, label_extract_test,
    label_merge, company_full_name_supplement,
    label_extract, search_related_companies, annual_report_api, company_relation_api, get_company_name)

# 导入数据管理模块
from service_crawer_manage.api import crawer_api
# 导入数据管理模块
from service_data_manage.api import data_manage

from utils.mongodb_util import MongodbUtil
from utils.minio_util import MinIoUtil
from utils.log_util import LogUtil
# import gradio as gr
# from gradio_util.gradio_util import Gradio_util

# 初始化日志
LogUtil.init(process_name="module_industry_chain_extension")
# 初始化数据库连接
MongodbUtil.connect()
MinIoUtil.connect()
SQLUtil.connect()

# 初始化API
app = FastAPI(
    title="产业链扩展接口服务",
    description="提供产业链扩展服务"
)

# 配置跨源资源共享（CORS）中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,  # 允许凭据
    allow_methods=["*"],  # 允许所有 HTTP 方法
    allow_headers=["*"],  # 允许所有请求头
)

app.include_router(ai_assistant.router)
app.include_router(common_extension.router)
app.include_router(company_summary.router)
app.include_router(download_document.router)
app.include_router(financial_suggestions.router)
app.include_router(get_key_companies.router)
app.include_router(industry_chain_map.router)
app.include_router(industry_evaluation.router)
app.include_router(key_companies.router)
app.include_router(node_analysis.router)
app.include_router(node_companies.router)
app.include_router(product_extension.router)
app.include_router(push_company_info.router)
app.include_router(related_companies.router)
app.include_router(research_report_extension.router)
app.include_router(search_document.router)
app.include_router(label_extract_test.router)
app.include_router(label_extract.router)
app.include_router(label_merge.router)
app.include_router(company_full_name_supplement.router)
app.include_router(search_related_companies.router)
app.include_router(annual_report_api.router)
app.include_router(company_relation_api.router)
app.include_router(company_relation_api.router)
# app.include_router(get_company_name.router)

# 注册数据管理模块路由
app.include_router(data_manage.api_router, prefix=ApiConfig.ROOT_ROUTE)
# 注册爬取网页路由
app.include_router(crawer_api.api_router, prefix=ApiConfig.ROOT_ROUTE)


# demo = Gradio_util.gradio_demo()

if __name__ == "__main__":
    # gr_app = gr.mount_gradio_app(app, demo, path="/gr")

    uvicorn.run(app, host=ApiConfig.SERVICE_IP, port=ApiConfig.SERVICE_PORT)
