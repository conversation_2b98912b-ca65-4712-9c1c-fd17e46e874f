#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/05/30 9:51
# <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
# @File         : sql_utils.py
# @Description  : 基于sqlalchemy的数据库curd操作工具类
"""
from sqlalchemy import create_engine, MetaData, Table, delete,func,update,insert
# from sqlalchemy import create_engine, MetaData, Table, delete
import sqlalchemy
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.sql import select
from configs.mysql_config import MysqlConfig
from sqlalchemy import inspect
from datetime import datetime, date
from sqlalchemy.exc import SQLAlchemyError
from typing import List


class SQLUtil(object):
    __engine = None
    __session = None

    ## 多数方法的返回值为模型实例，可使用model_to_dict或者models_to_list来获取字典列表

    @staticmethod
    def connect():
        """
        连接mysql
        :return:
        """
        SQLUtil._get_connection()

    @staticmethod
    def reconnect():
        """
        重新连接mysql
        :return:
        """
        SQLUtil.close()
        SQLUtil.connect()

    @staticmethod
    def close():
        """
        关闭mysql数据库连接
        :return:
        """
        SQLUtil.__session.close()

    @staticmethod
    def _get_connection():
        """
        获取mysql数据库连接
        :return:
        """
        if SQLUtil.__engine is None:
            SQLUtil.__engine = create_engine(
                "mysql+pymysql://",
                connect_args={
                    "host": MysqlConfig.MYSQL_HOST,
                    "user": MysqlConfig.MYSQL_USER,
                    "password": MysqlConfig.MYSQL_PASS,
                    "database": MysqlConfig.MYSQL_DB
                }
            )
            Session = sessionmaker(bind=SQLUtil.__engine)
            SQLUtil.__session = Session()

    @staticmethod
    def get_session():
        """
        获取mysql数据库连接
        :return:
        """
        return SQLUtil.__session
        

    @staticmethod
    def get_all_data(model):
        """
        查询某个表中的所有数据
        :param table_name: 表名
        :return:
        """
        return SQLUtil.__session.query(model).all()

    @staticmethod
    def get_data_by_id(model, primary_key_value):
        """
        根据主键查询单条数据
        :param model: SQLAlchemy ORM 模型（如 CompanyMain）
        :param primary_key_value: 主键值（如 id=1）
        :return: 返回模型实例，如果不存在则返回 None
        """
        return SQLUtil.__session.query(model).get(primary_key_value)

    @staticmethod
    def get_data_by_ids(model, primary_key_values: List[str]):
        """
        根据主键列表查询多条数据，暂不支持复合主键的表
        :param model: SQLAlchemy ORM 模型（如 CompanyMain）
        :param primary_key_values: 主键值字符串列表
        :return:
        """

        if primary_key_values is None or len(primary_key_values) == 0:
            return None

        primary_key = model.__table__.primary_key
        if len(primary_key.columns) > 1:
            raise ValueError("该方法不支持复合主键，请使用 get_data_by_id 逐个查询")

        pk_column = primary_key.columns[0]

        return SQLUtil.__session.query(model).filter(
            pk_column.in_(primary_key_values)
        ).all()

    @staticmethod
    def get_max_id(model, id_column='id'):
        """
        获取当前表中指定ID列的最大值
        :param model: SQLAlchemy ORM 模型类（如 CompanyMain）
        :param id_column: ID列名（默认为'id'）
        :return: 最大ID值（表中无数据时返回None）
        """
        try:
            # 获取ID列对象
            id_attr = getattr(model, id_column)

            # 查询最大值
            max_id = SQLUtil.__session.query(
                sqlalchemy.func.max(id_attr)
            ).scalar()

            return max_id
        except Exception as e:
            print(f"获取最大ID失败: {e}")
            return None

    @staticmethod
    def query_by_column(
        model,
        column_name,
        column_value,
        exact_match=True,
        limit=None,
        order_by=None
    ):
        """
        根据非主键列查询数据
        :param model: ORM 模型类（如 Order）
        :param column_name: 列名（如 "CompanyCode"）
        :param column_value: 查询值
        :param exact_match: 是否精确匹配（默认True，False时使用LIKE模糊查询）
        :param limit: 返回记录数限制（可选）
        :param order_by: 排序字段（如 "OrderCode.desc()"）
        :return: 查询结果列表
        """
        try:
            # 获取列对象
            column = getattr(model, column_name, None)
            if not column:
                raise ValueError(f"列 {column_name} 不存在")

            # 构建查询条件
            if exact_match:
                condition = column == column_value
            else:
                condition = column.like(f"%{column_value}%")

            # 构建查询
            query = SQLUtil.__session.query(model).filter(condition)

            # 添加排序
            if order_by:
                if isinstance(order_by, str):
                    if order_by.endswith(".desc()"):
                        order_column = getattr(model, order_by[:-7])
                        query = query.order_by(order_column.desc())
                    else:
                        order_column = getattr(model, order_by)
                        query = query.order_by(order_column)
                else:
                    query = query.order_by(order_by)

            # 添加限制
            if limit:
                query = query.limit(limit)

            return query.all()

        except Exception as e:
            print(f"查询失败: {e}")
            return []

    @staticmethod
    def get_table_columns(model_or_table_name):
        """
        根据模型类或表名获取表的字段名列表
        :param model_or_table_name: SQLAlchemy ORM 模型类 或 表名字符串
        :return: 字段名列表，失败返回None
        """
        try:
            # 判断传入的是模型类还是表名字符串
            if isinstance(model_or_table_name, str):
                # 如果是表名字符串，需要通过反射获取表对象
                inspector = inspect(SQLUtil.__session.get_bind())
                if not inspector.has_table(model_or_table_name):
                    print(f"表 '{model_or_table_name}' 不存在")
                    return None
                return [column['name'] for column in inspector.get_columns(model_or_table_name)]
            else:
                # 如果是模型类，直接获取其字段
                return [column.name for column in model_or_table_name.__table__.columns]

        except Exception as e:
            print(f"获取表字段失败: {e}")
            return None

    @staticmethod
    def insert_one(model, data_dict):
        """
        向表中插入单条数据
        :param model: SQLAlchemy ORM 模型（如 CompanyMain）
        :param data_dict: 要插入的数据（字典格式，如 {"name": "公司A", "address": "北京"}）
        :return: 插入后的模型实例（包含自动生成的主键）
        """
        try:
            # 创建模型实例
            new_record = model(**data_dict)

            # 添加到 session
            SQLUtil.__session.add(new_record)

            # 提交事务
            SQLUtil.__session.commit()

            # 刷新实例以获取数据库生成的默认值（如自增ID）
            SQLUtil.__session.refresh(new_record)

            return new_record
        except Exception as e:
            # 发生错误时回滚
            SQLUtil.__session.rollback()
            print(f"插入数据失败: {e}")
            return None

    @staticmethod
    def insert_many(model, data):
        """
        批量插入数据（带事务回滚）
        :param model: SQLAlchemy ORM 模型类（如 Order）
        :param data: 字典列表（每个字典对应一条记录）
        :return: 成功插入的记录数，失败时返回 0
        """
        try:
            # 批量插入数据（不触发 ORM 事件，高性能）
            SQLUtil.__session.bulk_insert_mappings(model, data)

            # 提交事务
            SQLUtil.__session.commit()

            # 返回插入的记录数
            return len(data)
        except Exception as e:
            # 发生错误时回滚事务
            SQLUtil.__session.rollback()
            print(f"批量插入失败: {e}")
            return 0

    @staticmethod
    def delete_by_id(model, value):
        """
        根据主键删除单条记录
        :param model: SQLAlchemy ORM 模型类
        :param primary_key_value: 主键值（如 OrderCode="ORD-001"）
        :return: 是否删除成功（True/False）
        """
        try:
            # 查询要删除的记录
            record = SQLUtil.__session.get(model, value)
            if not record:
                print("记录不存在")
                return False

            # 执行删除
            SQLUtil.__session.delete(record)
            SQLUtil.__session.commit()
            return True
        except Exception as e:
            SQLUtil.__session.rollback()
            print(f"删除记录失败: {e}")
            return False

    @staticmethod
    def delete_by_ids(model, id_list):
        """
        根据主键列表批量删除记录（高性能版）
        :param model: SQLAlchemy ORM 模型类（如 Order）
        :param id_list: 主键值列表（如 ["ORD-001", "ORD-002"]）
        :return: 成功删除的记录数，失败时返回 0
        """
        if not id_list:
            return 0  # 空列表直接返回

        try:
            # 获取主键列名（支持复合主键）
            primary_key = model.__table__.primary_key
            if len(primary_key.columns) > 1:
                raise ValueError("该方法不支持复合主键，请使用 bulk_delete 条件删除")

            # 动态生成删除语句（避免循环单条删除）
            pk_column = primary_key.columns.values()[0]
            stmt = (
                delete(model.__table__)
                .where(pk_column.in_(id_list))
            )

            # 执行批量删除
            result = SQLUtil.__session.execute(stmt)
            SQLUtil.__session.commit()
            return result.rowcount

        except Exception as e:
            SQLUtil.__session.rollback()
            print(f"批量删除失败: {e}")
            return 0

    @staticmethod
    def update_by_id(model, primary_key_value, update_data):
        """
        根据主键更新单条记录
        :param model: ORM 模型类（如 Order）
        :param primary_key_value: 主键值（如 "ORD-001"）
        :param update_data: 更新数据字典（如 {"CompanyCode": "NEW-CODE"}）
        :return: 更新后的完整对象，失败返回 None
        """
        try:
            # 查询要更新的记录
            record = SQLUtil.__session.get(model, primary_key_value)
            if not record:
                print("记录不存在")
                return None

            # 动态更新字段
            for key, value in update_data.items():
                if hasattr(record, key):
                    setattr(record, key, value)

            # 提交事务
            SQLUtil.__session.commit()

            # 刷新获取更新后的值（如数据库默认值）
            SQLUtil.__session.refresh(record)
            return record

        except Exception as e:
            SQLUtil.__session.rollback()
            print(f"更新失败: {e}")
            return None

    @staticmethod
    def bulk_update(model, conditions, update_data):
        """
        根据条件批量更新记录
        :param model: ORM 模型类
        :param conditions: 条件字典（如 {"CompanyCode": "OLD-CODE"}）
        :param update_data: 更新数据字典
        :return: 更新的记录数，失败返回 0
        """
        try:
            # 构建查询
            query = SQLUtil.__session.query(model)

            # 添加条件
            for col_name, value in conditions.items():
                column = getattr(model, col_name)
                query = query.filter(column == value)

            # 执行批量更新
            count = query.update(update_data)
            SQLUtil.__session.commit()
            return count

        except Exception as e:
            SQLUtil.__session.rollback()
            print(f"批量更新失败: {e}")
            return 0

        ## 样例
        # updated_count = SQLUtil.bulk_update(
        #     model=Order,
        #     conditions={"CompanyCode": "COMP-OLD"},
        #     update_data={"CompanyCode": "COMP-NEW"}
        # )
        # print(f"更新了 {updated_count} 条记录")

    @staticmethod
    def model_to_dict(obj, exclude_none=False, exclude_private=True):
        """
        将 SQLAlchemy ORM 对象转为字典
        :param obj: ORM 对象（如 Order 实例）
        :param exclude_none: 是否排除 None 值
        :param exclude_private: 是否排除 _ 开头的属性
        :return: 字典
        """
        if not obj:
            return {}

        result = {}
        for column in obj.__table__.columns:
            key = column.name
            value = getattr(obj, key)

            # 排除 None 值
            if exclude_none and value is None:
                continue

            # 排除私有属性
            if exclude_private and key.startswith('_'):
                continue

            # 处理特殊类型
            if isinstance(value, (datetime, date)):
                value = value.isoformat()
            elif hasattr(value, '__table__'):  # 处理关联对象
                value = SQLUtil.model_to_dict(value, exclude_none, exclude_private)

            result[key] = value

        return result

    @staticmethod
    def models_to_list(obj_list, exclude_none=False, exclude_private=True):
        """
        将 ORM 对象列表转为字典列表
        :param obj_list: ORM 对象列表
        :param exclude_none: 是否排除 None 值
        :param exclude_private: 是否排除 _ 开头的属性
        :return: 字典列表
        """
        return [
            SQLUtil.model_to_dict(obj, exclude_none, exclude_private)
            for obj in obj_list
        ]

    @staticmethod
    def count_records(model):
        """
        查询表中的总记录数
        :param model: SQLAlchemy ORM 模型类（如 Order）
        :return: 记录数（失败返回 -1）
        """
        try:
            count = SQLUtil.__session.query(model).count()
            return count
        except Exception as e:
            SQLUtil.__session.rollback()
            print(f"计数失败: {e}")
            return -1

    @staticmethod
    def count_records_with_condition(model, condition):
        """
        根据条件查询表中的记录数
        :param model: SQLAlchemy ORM 模型类（如 CompanyMain）
        :param condition: 查询条件（如 CompanyMain.status == 1）
        :return: 记录数（失败返回 -1）
        """
        try:
            count = SQLUtil.__session.query(model).filter(condition).count()
            return count
        except Exception as e:
            SQLUtil.__session.rollback()
            print(f"带条件计数失败: {e}")
            return -1

    @staticmethod
    def execute_sql(sql, search={}):
        """
        执行sql语句
        :param sql: sql语句
        :return:
        """
        result = SQLUtil.__session.execute(sql, search)
        SQLUtil.__session.commit()
        return result.fetchall()

        ##样例
        # SQLUtil.execute_sql(text("SELECT * FROM `order` limit 10;"))

    @staticmethod
    def exists(model, **kwargs):
        """
        检查数据库中是否存在符合条件的记录（仅统计status=1的记录）

        Args:
            model: SQLAlchemy 模型类
            **kwargs: 查询条件，键值对形式

        Returns:
            bool: 如果存在返回 True，否则返回 False
        """
        if not kwargs:
            raise ValueError("至少需要提供一个查询条件")

        try:
            # 创建查询，统计符合条件且status=1的记录数
            query = select(func.count()).filter(
                # 原有查询条件
                *[getattr(model, key) == value for key, value in kwargs.items()],
                # 设定status=1条件
                getattr(model, "status") == 1
            )

            # 执行查询
            count = SQLUtil.__session.execute(query).scalar()

            # 如果计数大于0，则存在记录
            return count > 0

        except Exception as e:
            print(f"检查记录存在性时出错: {e}")
            raise

    @staticmethod
    def get_max_value(model, field_name, prefix=None):
        """
        获取指定字段的最大值，可选择按前缀过滤

        Args:
            model: SQLAlchemy 模型类
            field_name: 字段名
            prefix: 前缀字符串（可选）

        Returns:
            最大值字符串或None
        """
        try:
            # 获取字段属性
            field = getattr(model, field_name)

            # 构建基础查询
            query = select(func.max(field))

            # 添加前缀过滤条件（如果有）
            if prefix:
                query = query.filter(field.like(f"{prefix}%"))

            # 执行查询
            result = SQLUtil.__session.execute(query).scalar()

            return result
        except Exception as e:
            print(f"获取指定字段的最大值时出错: {e}")
            raise

    @staticmethod
    def update(model, filter_kwargs, update_data):
        """
           更新符合条件的记录

           Args:
               model: SQLAlchemy 模型类
               filter_kwargs: 查询条件字典
               update_data: 更新数据字典

           Returns:
               int: 更新的记录数
           """
        try:
            query = update(model).filter(
                *[getattr(model, key) == value for key, value in filter_kwargs.items()]
            ).values(update_data)

            result = SQLUtil.__session.execute(query)
            SQLUtil.__session.commit()
            return result.rowcount
        except Exception as e:
            SQLUtil.__session.rollback()
            raise e

    @staticmethod
    def insert_data_one(model, data):
        """
        插入单条记录到数据库（适用于非自增主键）

        Args:
            model: SQLAlchemy 模型类
            data: 要插入的数据字典

        Returns:
            int: 插入记录的ID（如果有）或None
        """
        try:
            stmt = insert(model).values(**data)
            result = SQLUtil.__session.execute(stmt)
            SQLUtil.__session.commit()
            print("事务已提交")  # 新增日志
            # 如果主键是手动指定的（如CompanyCode），返回它
            if 'CompanyCode' in data:
                return data['CompanyCode']

            # 否则尝试返回自增主键
            return result.inserted_primary_key[0] if result.rowcount > 0 else None
        except Exception as e:
            SQLUtil.__session.rollback()
            raise e

    @staticmethod
    def query_by_multiple_columns(model, conditions_dict, exact_match=True):
        """
        根据多个列条件查询数据

        Args:
            model: SQLAlchemy ORM 模型类
            conditions_dict: 查询条件字典，如 {'job_id': 'xxx', 'status': 'FAILED'}
            exact_match: 是否精确匹配

        Returns:
            查询结果列表
        """
        try:
            query = SQLUtil.__session.query(model)

            for column_name, value in conditions_dict.items():
                column = getattr(model, column_name)
                if exact_match:
                    query = query.filter(column == value)
                else:
                    query = query.filter(column.like(f"%{value}%"))

            return query.all()

        except Exception as e:
            SQLUtil.__session.rollback()
            print(f"多条件查询失败: {e}")
            raise e
