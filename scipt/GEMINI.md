# Gemini Project Configuration

This file provides instructions for the Gemini agent to interact with this project.

## Build & Test Commands

- **Build:** `echo "No build command specified"`
- **Test:** `echo "No test command specified"`
- **Linter:** `echo "No linter specified"`

## Project Overview

- **Primary Language:** Python
- **Key Files:**
  - `a.py`: Main script for ...
  - `handle_wechat_articles.py`: Handles processing of WeChat articles.
- **Directory Structure:**
  - `downloaded_pdfs/`: Contains PDF files downloaded from articles.
  - `generated_jsons/`: Contains JSON output.

## Instructions for Gemini

- When asked to run the main script, use `python a.py`.
- Always format Python code using the `black` formatter if available.
