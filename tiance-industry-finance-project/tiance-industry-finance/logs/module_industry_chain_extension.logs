[2025-07-18 15:30:36,782] [module_industry_chain_extension 36233] [webpage_download_service._start_background_download: 218] INFO: 后台下载任务已启动: job_id=953c63d8aa214320a8142f7a84407a37
[2025-07-18 15:30:36,782] [module_industry_chain_extension 36233] [webpage_download_service.create_download_job: 186] INFO: 网页下载任务创建成功: job_id=953c63d8aa214320a8142f7a84407a37, 总数量=1
[2025-07-18 15:30:37,434] [module_industry_chain_extension 36233] [webpage_download_service._download_single_page: 65] ERROR: 下载脚本执行失败: Error: No module named 'service_crawer_manage'
NoneType: None
[2025-07-18 15:30:37,609] [module_industry_chain_extension 36233] [webpage_download_service._execute_download_job: 288] ERROR: 网页下载失败: 我市再认定13家龙头企业带动农业产业化 市级以上农业龙头企业达66家
NoneType: None
[2025-07-18 15:30:39,847] [module_industry_chain_extension 36233] [webpage_download_service._execute_download_job: 311] INFO: 下载任务完成: job_id=953c63d8aa214320a8142f7a84407a37, 成功=0, 失败=1
[2025-07-18 15:32:21,618] [module_industry_chain_extension 43406] [webpage_download_service._start_background_download: 218] INFO: 后台下载任务已启动: job_id=c4b1d09da9a44dfeb000a4162f0c13e7
[2025-07-18 15:32:21,619] [module_industry_chain_extension 43406] [webpage_download_service.create_download_job: 186] INFO: 网页下载任务创建成功: job_id=c4b1d09da9a44dfeb000a4162f0c13e7, 总数量=1
[2025-07-18 15:32:46,723] [module_industry_chain_extension 43406] [webpage_entity.check_and_update_job_status: 212] INFO: 更新任务状态: job_id=c4b1d09da9a44dfeb000a4162f0c13e7, CREATED -> RUNNING, 成功=0, 失败=0
[2025-07-18 15:32:47,080] [module_industry_chain_extension 43406] [webpage_query_service.query_job_status: 68] INFO: 自动更新任务状态完成: job_id=c4b1d09da9a44dfeb000a4162f0c13e7
[2025-07-18 15:32:47,122] [module_industry_chain_extension 43406] [webpage_query_service.query_job_status: 94] INFO: 查询任务状态成功: job_id=c4b1d09da9a44dfeb000a4162f0c13e7
[2025-07-18 15:32:52,541] [module_industry_chain_extension 43406] [webpage_download_service._download_single_page: 65] ERROR: 下载脚本执行失败: 2025-07-18 15:32:22,401 - INFO - Attempting to download with Pyppeteer: https://zhsme.org.cn/news/getNewsByNewsId?newsId=5d9dd3fc-22cb-45d2-a8fe-a3dcbbbb32a3
2025-07-18 15:32:52,494 - ERROR - Pyppeteer error converting https://zhsme.org.cn/news/getNewsByNewsId?newsId=5d9dd3fc-22cb-45d2-a8fe-a3dcbbbb32a3 to PDF: Browser closed unexpectedly:

Exception ignored in atexit callback: <function Launcher.launch.<locals>._close_process at 0x101dc08b0>
Traceback (most recent call last):
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pyppeteer/launcher.py", line 153, in _close_process
    self._loop.run_until_complete(self.killChrome())
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/asyncio/base_events.py", line 624, in run_until_complete
    self._check_closed()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/asyncio/base_events.py", line 515, in _check_closed
    raise RuntimeError('Event loop is closed')
RuntimeError: Event loop is closed
sys:1: RuntimeWarning: coroutine 'Launcher.killChrome' was never awaited
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
NoneType: None
[2025-07-18 15:32:52,771] [module_industry_chain_extension 43406] [webpage_download_service._execute_download_job: 288] ERROR: 网页下载失败: 我市再认定13家龙头企业带动农业产业化 市级以上农业龙头企业达66家
NoneType: None
[2025-07-18 15:32:55,031] [module_industry_chain_extension 43406] [webpage_download_service._execute_download_job: 311] INFO: 下载任务完成: job_id=c4b1d09da9a44dfeb000a4162f0c13e7, 成功=0, 失败=1
[2025-07-18 15:32:58,569] [module_industry_chain_extension 43406] [webpage_query_service.query_job_status: 94] INFO: 查询任务状态成功: job_id=c4b1d09da9a44dfeb000a4162f0c13e7
[2025-07-18 15:33:33,771] [module_industry_chain_extension 43406] [webpage_retry_service.retry_failed_downloads: 185] ERROR: 启动重新下载失败: type object 'SQLUtil' has no attribute 'query_by_multiple_columns'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/tiance-industry-finance-project/tiance-industry-finance/service_crawer_manage/service/webpage_retry_service.py", line 125, in retry_failed_downloads
    retry_details = SQLUtil.query_by_multiple_columns(
AttributeError: type object 'SQLUtil' has no attribute 'query_by_multiple_columns'
[2025-07-18 15:36:06,366] [module_industry_chain_extension 46047] [webpage_retry_service._start_background_retry: 206] INFO: 后台重试任务已启动，共 1 个网页
[2025-07-18 15:36:06,369] [module_industry_chain_extension 46047] [webpage_retry_service.retry_failed_downloads: 175] INFO: 重新下载任务启动成功，共 1 个网页待重试
[2025-07-18 15:36:12,704] [module_industry_chain_extension 46047] [webpage_retry_service._execute_retry_downloads: 265] INFO: 网页重试下载成功: 我市再认定13家龙头企业带动农业产业化 市级以上农业龙头企业达66家
[2025-07-18 15:36:14,705] [module_industry_chain_extension 46047] [webpage_retry_service._execute_retry_downloads: 287] INFO: 重试下载任务完成: 成功=1, 失败=0
[2025-07-18 15:36:15,024] [module_industry_chain_extension 46047] [webpage_retry_service._update_job_status_after_retry: 343] INFO: 更新任务状态: job_id=c4b1d09da9a44dfeb000a4162f0c13e7, 状态=COMPLETED, 成功=1, 失败=0
[2025-07-18 15:36:31,264] [module_industry_chain_extension 46047] [webpage_file_service.download_file_by_job_id: 284] INFO: 批量文件信息查询成功: job_id=c4b1d09da9a44dfeb000a4162f0c13e7, 文件数量=1
[2025-07-18 15:36:53,940] [module_industry_chain_extension 46047] [webpage_file_service.download_file_by_url: 181] INFO: 文件下载请求成功: url=https://zhsme.org.cn/news/getNewsByNewsId?newsId=5d9dd3fc-22cb-45d2-a8fe-a3dcbbbb32a3, file=我市再认定13家龙头企业带动农业产业化 市级以上农业龙头企业达66家.pdf
[2025-07-18 15:37:52,905] [module_industry_chain_extension 46047] [webpage_storage_service._start_background_storage: 159] INFO: 后台入库任务已启动，共 1 个文件
[2025-07-18 15:37:52,906] [module_industry_chain_extension 46047] [webpage_storage_service.store_files_by_job_id: 61] INFO: 文件入库任务启动成功: job_id=c4b1d09da9a44dfeb000a4162f0c13e7, 共 1 个文件待入库
[2025-07-18 15:37:53,292] [module_industry_chain_extension 46047] [webpage_storage_service._execute_storage_task: 216] INFO: 文件入库成功: 我市再认定13家龙头企业带动农业产业化 市级以上农业龙头企业达66家
[2025-07-18 15:37:53,294] [module_industry_chain_extension 46047] [webpage_storage_service._execute_storage_task: 233] INFO: 入库任务完成: 成功=1, 失败=0
[2025-07-18 15:39:59,825] [module_industry_chain_extension 46047] [webpage_download_service._start_background_download: 218] INFO: 后台下载任务已启动: job_id=24eef3f3f71d43cc82332895a4c0eb40
[2025-07-18 15:39:59,827] [module_industry_chain_extension 46047] [webpage_download_service.create_download_job: 186] INFO: 网页下载任务创建成功: job_id=24eef3f3f71d43cc82332895a4c0eb40, 总数量=1
[2025-07-18 15:40:40,662] [module_industry_chain_extension 46047] [webpage_download_service._download_single_page: 65] ERROR: 下载脚本执行失败: 2025-07-18 15:40:00,530 - INFO - Attempting to download with Pyppeteer: https://mp.weixin.qq.com/s?__biz=MzA4MTA1NzcyMw%3D%3D&mid=**********&idx=3&sn=507c649f9deb09c5cf91954869161aae&chksm=846b4768b31cce7e424d026f3d06431032304e640472a6000e4fe12a713f017a9731dce96f04&scene=27
2025-07-18 15:40:28,252 - INFO - Browser listening on: ws://127.0.0.1:50713/devtools/browser/9734add7-3315-45f4-97b5-af9aabe9c300
2025-07-18 15:40:28,370 - INFO - Attempt 1 to load page: https://mp.weixin.qq.com/s?__biz=MzA4MTA1NzcyMw%3D%3D&mid=**********&idx=3&sn=507c649f9deb09c5cf91954869161aae&chksm=846b4768b31cce7e424d026f3d06431032304e640472a6000e4fe12a713f017a9731dce96f04&scene=27
2025-07-18 15:40:28,994 - INFO - Page loaded successfully with strategy: domcontentloaded
2025-07-18 15:40:32,013 - INFO - Using fallback method to save page as PDF
2025-07-18 15:40:34,533 - INFO - Applying WeChat official account fixes...
2025-07-18 15:40:40,546 - INFO - WeChat content check: {'hasContent': None, 'hasTitle': None, 'contentLength': 0, 'hasCaptcha': True, 'pageTextLength': 10201, 'url': 'https://mp.weixin.qq.com/mp/wappoc_appmsgcaptcha?poc_token=HG76eWijck-nBgV6TNb7-hBnqkAhaMgIMw7VsGTg&target_url=https%3A%2F%2Fmp.weixin.qq.com%2Fs%3F__biz%3DMzA4MTA1NzcyMw%253D%253D%26mid%3D**********%26idx%3D3%26sn%3D507c649f9deb09c5cf91954869161aae%26chksm%3D846b4768b31cce7e424d026f3d06431032304e640472a6000e4fe12a713f017a9731dce96f04%26scene%3D27', 'captchaElementsCount': 2}
2025-07-18 15:40:40,546 - ERROR - WeChat verification/captcha detected, marking as failed
2025-07-18 15:40:40,547 - INFO - terminate chrome process...
2025-07-18 15:40:40,643 - INFO - Browser closed successfully
NoneType: None
[2025-07-18 15:40:40,843] [module_industry_chain_extension 46047] [webpage_query_service.query_job_status: 99] ERROR: 查询任务状态失败: job_id=c4b1d09da9a44dfeb000a4162f0c13e7, error=(pymysql.err.InternalError) Packet sequence number wrong - got 102 expected 10
[SQL: SELECT webpage_download_job.job_id AS webpage_download_job_job_id, webpage_download_job.job_name AS webpage_download_job_job_name, webpage_download_job.job_status AS webpage_download_job_job_status, webpage_download_job.total_count AS webpage_download_job_total_count, webpage_download_job.success_count AS webpage_download_job_success_count, webpage_download_job.failed_count AS webpage_download_job_failed_count, webpage_download_job.create_time AS webpage_download_job_create_time, webpage_download_job.start_time AS webpage_download_job_start_time, webpage_download_job.end_time AS webpage_download_job_end_time, webpage_download_job.update_time AS webpage_download_job_update_time 
FROM webpage_download_job 
WHERE webpage_download_job.job_id = %(pk_1)s]
[parameters: {'pk_1': 'c4b1d09da9a44dfeb000a4162f0c13e7'}]
(Background on this error at: https://sqlalche.me/e/20/2j85)
Traceback (most recent call last):
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/default.py", line 941, in do_execute
    cursor.execute(statement, parameters)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 757, in _read_packet
    raise err.InternalError(
pymysql.err.InternalError: Packet sequence number wrong - got 102 expected 10

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/tiance-industry-finance-project/tiance-industry-finance/service_crawer_manage/service/webpage_query_service.py", line 35, in query_job_status
    job = SQLUtil.get_data_by_id(WebpageDownloadJob, job_id)
  File "/Users/<USER>/Documents/GitHub/tiance-industry-finance-project/tiance-industry-finance/utils/sql_util.py", line 96, in get_data_by_id
    return SQLUtil.__session.query(model).get(primary_key_value)
  File "<string>", line 2, in get
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/util/deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/query.py", line 1127, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/query.py", line 1136, in _get_impl
    return self.session._get_impl(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 3873, in _get_impl
    return db_load_fn(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/loading.py", line 694, in load_on_pk_identity
    session.execute(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 2362, in execute
    return self._execute_internal(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 2247, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/context.py", line 305, in orm_execute_statement
    result = conn.execute(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1418, in execute
    return meth(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/sql/elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1640, in _execute_clauseelement
    ret = self._execute_context(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1846, in _execute_context
    return self._exec_single_context(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/default.py", line 941, in do_execute
    cursor.execute(statement, parameters)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 757, in _read_packet
    raise err.InternalError(
sqlalchemy.exc.InternalError: (pymysql.err.InternalError) Packet sequence number wrong - got 102 expected 10
[SQL: SELECT webpage_download_job.job_id AS webpage_download_job_job_id, webpage_download_job.job_name AS webpage_download_job_job_name, webpage_download_job.job_status AS webpage_download_job_job_status, webpage_download_job.total_count AS webpage_download_job_total_count, webpage_download_job.success_count AS webpage_download_job_success_count, webpage_download_job.failed_count AS webpage_download_job_failed_count, webpage_download_job.create_time AS webpage_download_job_create_time, webpage_download_job.start_time AS webpage_download_job_start_time, webpage_download_job.end_time AS webpage_download_job_end_time, webpage_download_job.update_time AS webpage_download_job_update_time 
FROM webpage_download_job 
WHERE webpage_download_job.job_id = %(pk_1)s]
[parameters: {'pk_1': 'c4b1d09da9a44dfeb000a4162f0c13e7'}]
(Background on this error at: https://sqlalche.me/e/20/2j85)
[2025-07-18 15:40:41,339] [module_industry_chain_extension 46047] [webpage_download_service._execute_download_job: 297] ERROR: 网页下载异常: 全省唯一！三水企业上榜2023年国家级水产良种场公示名单, error=(pymysql.err.InterfaceError) (0, '')
(Background on this error at: https://sqlalche.me/e/20/rvf5)
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/tiance-industry-finance-project/tiance-industry-finance/utils/sql_util.py", line 364, in update_by_id
    SQLUtil.__session.refresh(record)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 3164, in refresh
    loading.load_on_ident(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/loading.py", line 509, in load_on_ident
    return load_on_pk_identity(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/loading.py", line 694, in load_on_pk_identity
    session.execute(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 2362, in execute
    return self._execute_internal(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 2247, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/context.py", line 305, in orm_execute_statement
    result = conn.execute(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1418, in execute
    return meth(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/sql/elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1640, in _execute_clauseelement
    ret = self._execute_context(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1846, in _execute_context
    return self._exec_single_context(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 2358, in _handle_dbapi_exception
    raise exc_info[1].with_traceback(exc_info[2])
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/default.py", line 941, in do_execute
    cursor.execute(statement, parameters)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 1206, in read
    self._read_result_packet(first_packet)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 1282, in _read_result_packet
    self._get_descriptions()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 1367, in _get_descriptions
    field = self.connection._read_packet(FieldDescriptorPacket)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 763, in _read_packet
    recv_data = self._read_bytes(bytes_to_read)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 782, in _read_bytes
    data = self._rfile.read(num_bytes)
AttributeError: 'NoneType' object has no attribute 'read'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1127, in _rollback_impl
    self.engine.dialect.do_rollback(self.connection)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/default.py", line 699, in do_rollback
    dbapi_connection.rollback()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 492, in rollback
    self._execute_command(COMMAND.COM_QUERY, "ROLLBACK")
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 843, in _execute_command
    raise err.InterfaceError(0, "")
pymysql.err.InterfaceError: (0, '')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/tiance-industry-finance-project/tiance-industry-finance/service_crawer_manage/service/webpage_download_service.py", line 283, in _execute_download_job
    SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
  File "/Users/<USER>/Documents/GitHub/tiance-industry-finance-project/tiance-industry-finance/utils/sql_util.py", line 368, in update_by_id
    SQLUtil.__session.rollback()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 1990, in rollback
    self._transaction.rollback(_to_root=True)
  File "<string>", line 2, in rollback
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 1395, in rollback
    raise rollback_err[1].with_traceback(rollback_err[2])
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 1360, in rollback
    t[1].rollback()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 2610, in rollback
    self._do_rollback()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 2729, in _do_rollback
    self._close_impl(try_deactivate=True)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 2712, in _close_impl
    self._connection_rollback_impl()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 2704, in _connection_rollback_impl
    self.connection._rollback_impl()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1129, in _rollback_impl
    self._handle_dbapi_exception(e, None, None, None, None)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1127, in _rollback_impl
    self.engine.dialect.do_rollback(self.connection)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/default.py", line 699, in do_rollback
    dbapi_connection.rollback()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 492, in rollback
    self._execute_command(COMMAND.COM_QUERY, "ROLLBACK")
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 843, in _execute_command
    raise err.InterfaceError(0, "")
sqlalchemy.exc.InterfaceError: (pymysql.err.InterfaceError) (0, '')
(Background on this error at: https://sqlalche.me/e/20/rvf5)
[2025-07-18 15:40:44,250] [module_industry_chain_extension 46047] [webpage_download_service._execute_download_job: 311] INFO: 下载任务完成: job_id=24eef3f3f71d43cc82332895a4c0eb40, 成功=0, 失败=1
[2025-07-18 15:43:09,145] [module_industry_chain_extension 46047] [webpage_download_service._start_background_download: 218] INFO: 后台下载任务已启动: job_id=745347798c3f4aa8b946686a78a9b0a1
[2025-07-18 15:43:09,147] [module_industry_chain_extension 46047] [webpage_download_service.create_download_job: 186] INFO: 网页下载任务创建成功: job_id=745347798c3f4aa8b946686a78a9b0a1, 总数量=1
[2025-07-18 15:43:21,890] [module_industry_chain_extension 46047] [webpage_download_service._execute_download_job: 280] INFO: 网页下载成功: “虾先生”：消费帮扶助“湛品”出圈
[2025-07-18 15:43:24,126] [module_industry_chain_extension 46047] [webpage_download_service._execute_download_job: 311] INFO: 下载任务完成: job_id=745347798c3f4aa8b946686a78a9b0a1, 成功=1, 失败=0
[2025-07-18 15:43:27,281] [module_industry_chain_extension 46047] [webpage_query_service.query_job_status: 94] INFO: 查询任务状态成功: job_id=745347798c3f4aa8b946686a78a9b0a1
[2025-07-18 15:43:48,113] [module_industry_chain_extension 46047] [webpage_storage_service._start_background_storage: 159] INFO: 后台入库任务已启动，共 1 个文件
[2025-07-18 15:43:48,114] [module_industry_chain_extension 46047] [webpage_storage_service.store_files_by_job_id: 61] INFO: 文件入库任务启动成功: job_id=745347798c3f4aa8b946686a78a9b0a1, 共 1 个文件待入库
[2025-07-18 15:43:59,922] [module_industry_chain_extension 46047] [webpage_storage_service._execute_storage_task: 216] INFO: 文件入库成功: “虾先生”：消费帮扶助“湛品”出圈
[2025-07-18 15:43:59,922] [module_industry_chain_extension 46047] [webpage_storage_service._execute_storage_task: 233] INFO: 入库任务完成: 成功=1, 失败=0
