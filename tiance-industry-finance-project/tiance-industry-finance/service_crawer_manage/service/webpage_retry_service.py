#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 16:30:00
# <AUTHOR> Assistant
# @File         : webpage_retry_service.py
# @Description  : 网页重新下载服务类
"""

import os
import asyncio
import threading
from datetime import datetime
from typing import List, Dict, Any, Union
from utils.sql_util import SQLUtil
from utils.log_util import LogUtil
from service_crawer_manage.entity.webpage_entity import WebpageDownloadDetail, WebpageDownloadJob, DownloadStatus, JobStatus


class WebpageRetryService:
    """网页重新下载服务类"""

    # 自定义临时目录
    TEMP_DOWNLOAD_DIR = "./temp_webpage_downloads"

    @staticmethod
    def _retry_download_single_page(webpage_name: str, webpage_url: str, download_dir: str) -> bool:
        """
        同步重试下载单个网页，避免多线程问题

        Args:
            webpage_name: 网页名称
            webpage_url: 网页URL
            download_dir: 下载目录

        Returns:
            是否下载成功
        """
        try:
            import subprocess
            import sys

            # 使用subprocess在新进程中执行重试下载，避免多线程问题
            script_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                                     'script', 'retry_single_page.py')

            # 如果脚本不存在，创建一个简单的重试脚本
            if not os.path.exists(script_path):
                WebpageRetryService._create_retry_script(script_path)

            # 执行重试下载
            result = subprocess.run([
                sys.executable, script_path,
                '--name', webpage_name,
                '--url', webpage_url,
                '--dir', download_dir
            ], capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                # 检查文件是否存在
                pdf_path = os.path.join(download_dir, f"{webpage_name}.pdf")
                return os.path.exists(pdf_path)
            else:
                LogUtil.error(f"重试脚本执行失败: {result.stderr}")
                return False

        except Exception as e:
            LogUtil.error(f"同步重试下载失败: {webpage_name}, error={str(e)}")
            return False

    @staticmethod
    def _create_retry_script(script_path: str):
        """创建重试下载脚本"""
        script_content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
import argparse
import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--name', required=True)
    parser.add_argument('--url', required=True)
    parser.add_argument('--dir', required=True)
    args = parser.parse_args()

    try:
        from service_crawer_manage.script.retry_failed_downloads import download_with_fallback_strategy
        success = await download_with_fallback_strategy(args.name, args.url, args.dir)
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
'''
        os.makedirs(os.path.dirname(script_path), exist_ok=True)
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        os.chmod(script_path, 0o755)
    
    @staticmethod
    def retry_failed_downloads(job_id: str = None, webpage_urls: List[str] = None) -> Dict[str, Any]:
        """
        重新下载失败的网页
        
        Args:
            job_id: 任务ID（可选，如果提供则重试该任务的所有失败下载）
            webpage_urls: 网页地址列表（可选，如果提供则重试指定的网页）
            
        Returns:
            重试结果
        """
        try:
            SQLUtil.connect()
            
            # 获取需要重试的下载记录
            if job_id:
                # 按任务ID重试
                retry_details = SQLUtil.query_by_multiple_columns(
                    WebpageDownloadDetail,
                    {
                        'job_id': job_id,
                        'download_status': DownloadStatus.FAILED.value
                    }
                )
                for detail in retry_details:
                    SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                        'download_status': DownloadStatus.DOWNLOADING,
                        'error_message': None
                    })
                if not retry_details:
                    return {
                        "success": True,
                        "message": f"任务 {job_id} 没有失败的下载记录",
                        "retry_count": 0
                    }
            elif webpage_urls:
                # 按网页地址重试
                retry_details = []
                for url in webpage_urls:
                    details = SQLUtil.query_by_multiple_columns(
                        WebpageDownloadDetail,
                        {
                            'webpage_url': url
                        }
                    )
                    for detail in details:
                        SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                        'download_status': DownloadStatus.DOWNLOADING,
                        'error_message': None
                    })
                    retry_details.extend(details)
                
                if not retry_details:
                    return {
                        "success": True,
                        "message": "指定的网页地址没有失败的下载记录",
                        "retry_count": 0
                    }
            else:
                raise ValueError("必须提供 job_id 或 webpage_urls 参数")
            
            # 提取详情ID列表，避免跨线程传递ORM对象
            detail_ids = [detail.id for detail in retry_details]

            # 启动后台重试任务
            WebpageRetryService._start_background_retry(detail_ids)
            
            LogUtil.info(f"重新下载任务启动成功，共 {len(retry_details)} 个网页待重试")
            
            return {
                "success": True,
                "message": f"重新下载任务启动成功，共 {len(retry_details)} 个网页待重试",
                "retry_count": len(retry_details)
            }
            
        except Exception as e:
            error_msg = f"启动重新下载失败: {str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            try:
                SQLUtil.close()
            except:
                pass
    
    @staticmethod
    def _start_background_retry(detail_ids: List[int]):
        """启动后台重试任务"""
        def run_retry():
            try:
                # 在新线程中运行异步重试
                asyncio.run(WebpageRetryService._execute_retry_downloads(detail_ids))
            except Exception as e:
                LogUtil.error(f"后台重试任务执行失败: error={str(e)}")

        # 启动后台线程
        retry_thread = threading.Thread(target=run_retry, daemon=True)
        retry_thread.start()
        LogUtil.info(f"后台重试任务已启动，共 {len(detail_ids)} 个网页")
    
    @staticmethod
    async def _execute_retry_downloads(detail_ids: List[int]):
        """执行重试下载"""
        try:
            SQLUtil.connect()

            # 确保临时目录存在
            os.makedirs(WebpageRetryService.TEMP_DOWNLOAD_DIR, exist_ok=True)

            success_count = 0
            failed_count = 0

            # 不再需要导入，使用同步重试方法

            for detail_id in detail_ids:
                # 重新查询详情记录，避免会话绑定问题
                detail = SQLUtil.get_data_by_id(WebpageDownloadDetail, detail_id)
                if not detail:
                    LogUtil.warning(f"详情记录不存在: detail_id={detail_id}")
                    continue
                try:
                    # 更新重试次数和状态
                    SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                        'download_status': DownloadStatus.DOWNLOADING.value,
                        'retry_count': detail.retry_count + 1,
                        'error_message': None
                    })
                    
                    # 删除原有的失败文件（如果存在）
                    if detail.file_path and os.path.exists(detail.file_path):
                        try:
                            os.remove(detail.file_path)
                            LogUtil.info(f"删除原有失败文件: {detail.file_path}")
                        except Exception as e:
                            LogUtil.warning(f"删除原有文件失败: {detail.file_path}, error={str(e)}")
                    
                    # 执行重试下载 - 使用同步方法避免多线程问题
                    success = WebpageRetryService._retry_download_single_page(
                        detail.webpage_name,
                        detail.webpage_url,
                        WebpageRetryService.TEMP_DOWNLOAD_DIR
                    )
                    
                    if success:
                        # 检查文件是否存在并获取文件信息
                        file_path = os.path.join(WebpageRetryService.TEMP_DOWNLOAD_DIR, f"{detail.webpage_name}.pdf")
                        file_size = os.path.getsize(file_path) if os.path.exists(file_path) else None
                        
                        # 更新为成功状态
                        SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                            'download_status': DownloadStatus.SUCCESS.value,
                            'file_path': file_path,
                            'file_size': file_size,
                            'download_time': datetime.now(),
                            'error_message': None
                        })
                        success_count += 1
                        LogUtil.info(f"网页重试下载成功: {detail.webpage_name}")
                    else:
                        # 更新为失败状态
                        SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                            'download_status': DownloadStatus.FAILED.value,
                            'error_message': "重试下载失败"
                        })
                        failed_count += 1
                        LogUtil.error(f"网页重试下载失败: {detail.webpage_name}")
                        
                except Exception as e:
                    # 更新为失败状态
                    SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                        'download_status': DownloadStatus.FAILED.value,
                        'error_message': f"重试异常: {str(e)}"
                    })
                    failed_count += 1
                    LogUtil.error(f"网页重试下载异常: {detail.webpage_name}, error={str(e)}")
                
                # 请求间隔
                await asyncio.sleep(2)
            
            LogUtil.info(f"重试下载任务完成: 成功={success_count}, 失败={failed_count}")

            # 更新相关任务的状态
            WebpageRetryService._update_job_status_after_retry(detail_ids, success_count, failed_count)

        except Exception as e:
            LogUtil.error(f"执行重试下载任务失败: error={str(e)}")
        finally:
            try:
                SQLUtil.close()
            except:
                pass

    @staticmethod
    def _update_job_status_after_retry(detail_ids: List[int], success_count: int, failed_count: int):
        """重试完成后更新任务状态"""
        try:
            SQLUtil.connect()

            # 获取涉及的任务ID
            job_ids = set()
            for detail_id in detail_ids:
                detail = SQLUtil.get_data_by_id(WebpageDownloadDetail, detail_id)
                if detail:
                    job_ids.add(detail.job_id)

            # 更新每个任务的统计信息
            for job_id in job_ids:
                # 重新统计该任务的成功和失败数量
                details = SQLUtil.query_by_column(
                    WebpageDownloadDetail,
                    'job_id',
                    job_id,
                    exact_match=True
                )

                success_count = len([d for d in details if d.download_status == DownloadStatus.SUCCESS.value])
                failed_count = len([d for d in details if d.download_status == DownloadStatus.FAILED.value])
                stored_count = len([d for d in details if d.download_status == DownloadStatus.STORED.value])

                # 确定任务状态
                if failed_count == 0:
                    job_status = JobStatus.COMPLETED.value
                elif success_count + stored_count == 0:
                    job_status = JobStatus.FAILED.value
                else:
                    job_status = JobStatus.COMPLETED.value  # 部分成功也算完成

                # 更新任务状态
                SQLUtil.update_by_id(WebpageDownloadJob, job_id, {
                    'job_status': job_status,
                    'success_count': success_count + stored_count,
                    'failed_count': failed_count,
                    'end_time': datetime.now()
                })

                LogUtil.info(f"更新任务状态: job_id={job_id}, 状态={job_status}, 成功={success_count + stored_count}, 失败={failed_count}")

        except Exception as e:
            LogUtil.error(f"更新任务状态失败: error={str(e)}")
        finally:
            try:
                SQLUtil.close()
            except:
                pass
    
    @staticmethod
    def retry_by_detail_ids(detail_ids: List[int]) -> Dict[str, Any]:
        """
        通过详情ID列表重新下载
        
        Args:
            detail_ids: 详情ID列表
            
        Returns:
            重试结果
        """
        try:
            SQLUtil.connect()
            
            # 验证详情记录并过滤失败的记录
            valid_detail_ids = []
            for detail_id in detail_ids:
                detail = SQLUtil.get_data_by_id(WebpageDownloadDetail, detail_id)
                if detail and detail.download_status == DownloadStatus.FAILED.value:
                    valid_detail_ids.append(detail_id)

            if not valid_detail_ids:
                return {
                    "success": True,
                    "message": "指定的记录中没有失败的下载",
                    "retry_count": 0
                }

            # 启动后台重试任务
            WebpageRetryService._start_background_retry(valid_detail_ids)
            
            LogUtil.info(f"按详情ID重新下载任务启动成功，共 {len(valid_detail_ids)} 个网页待重试")

            return {
                "success": True,
                "message": f"重新下载任务启动成功，共 {len(valid_detail_ids)} 个网页待重试",
                "retry_count": len(valid_detail_ids)
            }
            
        except Exception as e:
            error_msg = f"按详情ID启动重新下载失败: {str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            try:
                SQLUtil.close()
            except:
                pass
