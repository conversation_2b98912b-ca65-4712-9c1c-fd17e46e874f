#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 16:00:00
# <AUTHOR> Assistant
# @File         : webpage_file_service.py
# @Description  : 网页文件服务类
"""

import os
import re
from typing import Dict, Any, Optional, Tuple
from urllib.parse import quote
from fastapi import HTTPException
from fastapi.responses import FileResponse
from utils.sql_util import SQLUtil
from utils.log_util import LogUtil
from service_crawer_manage.entity.webpage_entity import WebpageDownloadJob, WebpageDownloadDetail, DownloadStatus, WebpageStatusManager


class WebpageFileService:
    """网页文件服务类"""

    @staticmethod
    def _sanitize_filename(filename: str) -> str:
        """
        安全化文件名，确保可以正确处理

        Args:
            filename: 原始文件名

        Returns:
            安全的文件名
        """
        # 移除或替换不安全的字符
        # 保留中文字符、字母、数字、下划线、连字符和点号
        safe_filename = re.sub(r'[<>:"/\\|?*]', '_', filename)

        # 确保文件名不为空且不超过255个字符
        if not safe_filename.strip():
            safe_filename = "unnamed_file"

        # 截断过长的文件名（保留扩展名）
        if len(safe_filename) > 255:
            name_part, ext_part = os.path.splitext(safe_filename)
            max_name_len = 255 - len(ext_part)
            safe_filename = name_part[:max_name_len] + ext_part

        return safe_filename
    
    @staticmethod
    def download_file_by_job_and_name(job_id: str, webpage_url: str, webpage_name: str) -> Tuple[str, str]:
        """
        通过job_id + 网页地址 + 保存网页名字下载文件
        
        Args:
            job_id: 任务ID
            webpage_url: 网页地址
            webpage_name: 保存网页的名字
            
        Returns:
            (file_path, filename) 元组
        """
        try:
            SQLUtil.connect()
            
            # 查询具体的下载记录
            details = SQLUtil.query_by_multiple_columns(
                WebpageDownloadDetail,
                {
                    'job_id': job_id,
                    'webpage_url': webpage_url,
                    'webpage_name': webpage_name
                }
            )
            
            if not details:
                raise HTTPException(
                    status_code=404,
                    detail=f"未找到匹配的下载记录: job_id={job_id}, url={webpage_url}, name={webpage_name}"
                )
            
            detail = details[0]

            # 检查并更新状态
            status_updated = WebpageStatusManager.check_and_update_detail_status(detail)
            if status_updated:
                try:
                    SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                        'download_status': detail.download_status,
                        'error_message': detail.error_message
                    })
                    LogUtil.info(f"文件下载时自动更新状态: detail_id={detail.id}")
                except Exception as update_error:
                    LogUtil.error(f"保存状态更新失败: detail_id={detail.id}, error={str(update_error)}")

            # 检查下载状态
            if detail.download_status not in [DownloadStatus.SUCCESS.value, DownloadStatus.STORED.value]:
                raise HTTPException(
                    status_code=400,
                    detail=f"文件未下载成功，当前状态: {detail.download_status}"
                )
            
            # 检查文件是否存在
            if not detail.file_path or not os.path.exists(detail.file_path):
                raise HTTPException(
                    status_code=404,
                    detail="文件不存在或已被删除"
                )
            
            # 安全化文件名
            safe_filename = WebpageFileService._sanitize_filename(f"{webpage_name}.pdf")
            LogUtil.info(f"文件下载请求成功: job_id={job_id}, file={safe_filename}")

            return detail.file_path, safe_filename
            
        except HTTPException:
            raise
        except Exception as e:
            error_msg = f"下载文件失败: {str(e)}"
            LogUtil.error(error_msg)
            raise HTTPException(status_code=500, detail=error_msg)
        finally:
            try:
                SQLUtil.close()
            except:
                pass
    
    @staticmethod
    def download_file_by_url(webpage_url: str) -> Tuple[str, str]:
        """
        通过网页地址下载文件（返回最新的成功下载）
        
        Args:
            webpage_url: 网页地址
            
        Returns:
            (file_path, filename) 元组
        """
        try:
            SQLUtil.connect()
            
            # 查询该URL的所有成功下载记录
            details = SQLUtil.query_by_column(
                WebpageDownloadDetail,
                'webpage_url',
                webpage_url,
                exact_match=True
            )
            
            # 检查并更新所有详情状态
            for detail in details:
                status_updated = WebpageStatusManager.check_and_update_detail_status(detail)
                if status_updated:
                    try:
                        SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                            'download_status': detail.download_status,
                            'error_message': detail.error_message
                        })
                    except Exception as update_error:
                        LogUtil.error(f"保存状态更新失败: detail_id={detail.id}, error={str(update_error)}")

            # 过滤成功的下载记录
            success_details = [
                d for d in details
                if d.download_status in [DownloadStatus.SUCCESS.value, DownloadStatus.STORED.value]
                and d.file_path and os.path.exists(d.file_path)
            ]
            
            if not success_details:
                raise HTTPException(
                    status_code=404,
                    detail=f"未找到该网页地址的成功下载记录: {webpage_url}"
                )
            
            # 选择最新的下载记录
            latest_detail = max(success_details, key=lambda x: x.download_time or x.create_time)
            
            # 安全化文件名
            safe_filename = WebpageFileService._sanitize_filename(f"{latest_detail.webpage_name}.pdf")
            LogUtil.info(f"文件下载请求成功: url={webpage_url}, file={safe_filename}")

            return latest_detail.file_path, safe_filename
            
        except HTTPException:
            raise
        except Exception as e:
            error_msg = f"下载文件失败: {str(e)}"
            LogUtil.error(error_msg)
            raise HTTPException(status_code=500, detail=error_msg)
        finally:
            try:
                SQLUtil.close()
            except:
                pass
    
    @staticmethod
    def download_file_by_job_id(job_id: str) -> Dict[str, Any]:
        """
        通过job_id批量下载文件（返回文件列表信息）
        
        Args:
            job_id: 任务ID
            
        Returns:
            文件列表信息
        """
        try:
            SQLUtil.connect()
            
            # 验证任务是否存在
            job = SQLUtil.get_data_by_id(WebpageDownloadJob, job_id)
            if not job:
                raise HTTPException(
                    status_code=404,
                    detail=f"任务不存在: job_id={job_id}"
                )
            
            # 查询该任务的所有下载记录
            details = SQLUtil.query_by_column(
                WebpageDownloadDetail,
                'job_id',
                job_id,
                exact_match=True
            )

            # 检查并更新状态
            status_updated = WebpageStatusManager.check_and_update_job_status(job, details)
            if status_updated:
                try:
                    # 更新任务记录
                    SQLUtil.update_by_id(WebpageDownloadJob, job_id, {
                        'job_status': job.job_status,
                        'success_count': job.success_count,
                        'failed_count': job.failed_count,
                        'end_time': job.end_time
                    })

                    # 更新详情记录
                    for detail in details:
                        SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                            'download_status': detail.download_status,
                            'error_message': detail.error_message
                        })

                    LogUtil.info(f"文件列表查询时自动更新状态: job_id={job_id}")
                except Exception as update_error:
                    LogUtil.error(f"保存状态更新失败: job_id={job_id}, error={str(update_error)}")

            # 过滤成功的下载记录
            success_details = [
                d for d in details
                if d.download_status in [DownloadStatus.SUCCESS.value, DownloadStatus.STORED.value]
                and d.file_path and os.path.exists(d.file_path)
            ]
            
            if not success_details:
                raise HTTPException(
                    status_code=404,
                    detail=f"该任务没有成功下载的文件: job_id={job_id}"
                )
            
            # 构建文件列表
            file_list = []
            for detail in success_details:
                file_info = {
                    "id": detail.id,
                    "webpage_url": detail.webpage_url,
                    "webpage_name": detail.webpage_name,
                    "data_type": detail.data_type,
                    "file_path": detail.file_path,
                    "file_size": detail.file_size,
                    "download_time": detail.download_time.strftime('%Y-%m-%d %H:%M:%S') if detail.download_time else None
                }
                file_list.append(file_info)
            
            result = {
                "job_id": job_id,
                "job_name": job.job_name,
                "total_files": len(file_list),
                "files": file_list
            }
            
            LogUtil.info(f"批量文件信息查询成功: job_id={job_id}, 文件数量={len(file_list)}")
            return result
            
        except HTTPException:
            raise
        except Exception as e:
            error_msg = f"查询文件列表失败: {str(e)}"
            LogUtil.error(error_msg)
            raise HTTPException(status_code=500, detail=error_msg)
        finally:
            try:
                SQLUtil.close()
            except:
                pass
    
    @staticmethod
    def get_file_response(file_path: str, filename: str) -> FileResponse:
        """
        创建文件响应对象

        Args:
            file_path: 文件路径
            filename: 文件名

        Returns:
            FastAPI FileResponse对象
        """
        # 对文件名进行URL编码以支持中文字符
        encoded_filename = quote(filename, safe='')

        return FileResponse(
            path=file_path,
            filename=filename,
            media_type='application/pdf',
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}"
            }
        )
