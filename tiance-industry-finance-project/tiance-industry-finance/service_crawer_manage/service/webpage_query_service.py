#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 15:30:00
# <AUTHOR> Assistant
# @File         : webpage_query_service.py
# @Description  : 网页查询服务类
"""

from typing import Dict, Any, List, Optional
from utils.sql_util import SQLUtil
from utils.log_util import LogUtil
from utils.page_util import PageUtil
from service_crawer_manage.entity.webpage_entity import WebpageDownloadJob, WebpageDownloadDetail, WebpageStatusManager


class WebpageQueryService:
    """网页查询服务类"""
    
    @staticmethod
    def query_job_status(job_id: str) -> Dict[str, Any]:
        """
        查询任务状态（自动检查并更新状态）

        Args:
            job_id: 任务ID

        Returns:
            任务状态信息
        """
        try:
            SQLUtil.connect()

            # 查询任务信息
            job = SQLUtil.get_data_by_id(WebpageDownloadJob, job_id)
            if not job:
                raise ValueError(f"任务不存在: job_id={job_id}")

            # 查询详情统计
            details = SQLUtil.query_by_column(
                WebpageDownloadDetail,
                'job_id',
                job_id,
                exact_match=True
            )

            # 检查并更新状态
            status_updated = WebpageStatusManager.check_and_update_job_status(job, details)

            # 如果状态有更新，需要保存到数据库
            if status_updated:
                try:
                    # 更新任务记录
                    SQLUtil.update_by_id(WebpageDownloadJob, job_id, {
                        'job_status': job.job_status,
                        'success_count': job.success_count,
                        'failed_count': job.failed_count,
                        'end_time': job.end_time
                    })

                    # 更新详情记录
                    for detail in details:
                        SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                            'download_status': detail.download_status,
                            'error_message': detail.error_message
                        })

                    LogUtil.info(f"自动更新任务状态完成: job_id={job_id}")

                except Exception as update_error:
                    LogUtil.error(f"保存状态更新失败: job_id={job_id}, error={str(update_error)}")
                    # 继续执行，不影响查询结果
            
            # 统计各状态数量
            status_count = {}
            for detail in details:
                status = detail.download_status
                status_count[status] = status_count.get(status, 0) + 1
            
            result = {
                "job_id": job.job_id,
                "job_name": job.job_name,
                "job_status": job.job_status,
                "total_count": job.total_count,
                "success_count": job.success_count,
                "failed_count": job.failed_count,
                "create_time": job.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                "start_time": job.start_time.strftime('%Y-%m-%d %H:%M:%S') if job.start_time else None,
                "end_time": job.end_time.strftime('%Y-%m-%d %H:%M:%S') if job.end_time else None,
                "update_time": job.update_time.strftime('%Y-%m-%d %H:%M:%S'),
                "status_detail": status_count
            }
            
            LogUtil.info(f"查询任务状态成功: job_id={job_id}")
            return result
            
        except Exception as e:
            error_msg = f"查询任务状态失败: job_id={job_id}, error={str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            try:
                SQLUtil.close()
            except:
                pass
    
    @staticmethod
    def query_job_details(job_id: str, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        查询任务详情（分页，自动检查并更新状态）

        Args:
            job_id: 任务ID
            page: 页码
            page_size: 每页大小

        Returns:
            分页的任务详情
        """
        try:
            SQLUtil.connect()

            # 验证任务是否存在
            job = SQLUtil.get_data_by_id(WebpageDownloadJob, job_id)
            if not job:
                raise ValueError(f"任务不存在: job_id={job_id}")

            # 查询详情列表
            details = SQLUtil.query_by_column(
                WebpageDownloadDetail,
                'job_id',
                job_id,
                exact_match=True
            )

            # 检查并更新状态
            status_updated = WebpageStatusManager.check_and_update_job_status(job, details)

            # 如果状态有更新，需要保存到数据库
            if status_updated:
                try:
                    # 更新任务记录
                    SQLUtil.update_by_id(WebpageDownloadJob, job_id, {
                        'job_status': job.job_status,
                        'success_count': job.success_count,
                        'failed_count': job.failed_count,
                        'end_time': job.end_time
                    })

                    # 更新详情记录
                    for detail in details:
                        SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                            'download_status': detail.download_status,
                            'error_message': detail.error_message
                        })

                    LogUtil.info(f"查询详情时自动更新状态完成: job_id={job_id}")

                except Exception as update_error:
                    LogUtil.error(f"保存详情状态更新失败: job_id={job_id}, error={str(update_error)}")
                    # 继续执行，不影响查询结果
            
            # 转换为字典列表
            detail_list = []
            for detail in details:
                detail_dict = {
                    "id": detail.id,
                    "webpage_url": detail.webpage_url,
                    "webpage_name": detail.webpage_name,
                    "data_type": detail.data_type,
                    "download_status": detail.download_status,
                    "file_path": detail.file_path,
                    "file_size": detail.file_size,
                    "error_message": detail.error_message,
                    "retry_count": detail.retry_count,
                    "create_time": detail.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "download_time": detail.download_time.strftime('%Y-%m-%d %H:%M:%S') if detail.download_time else None,
                    "update_time": detail.update_time.strftime('%Y-%m-%d %H:%M:%S')
                }
                detail_list.append(detail_dict)
            
            # 分页处理
            paginated_result = PageUtil.paginate_list(detail_list, page, page_size)
            
            LogUtil.info(f"查询任务详情成功: job_id={job_id}, page={page}, page_size={page_size}")
            return paginated_result
            
        except Exception as e:
            error_msg = f"查询任务详情失败: job_id={job_id}, error={str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            try:
                SQLUtil.close()
            except:
                pass
    
    @staticmethod
    def query_jobs_list(page: int = 1, page_size: int = 20, job_status: Optional[str] = None) -> Dict[str, Any]:
        """
        查询任务列表（分页，自动检查并更新状态）

        Args:
            page: 页码
            page_size: 每页大小
            job_status: 任务状态过滤（可选）

        Returns:
            分页的任务列表
        """
        try:
            SQLUtil.connect()

            # 构建查询条件
            if job_status:
                jobs = SQLUtil.query_by_column(
                    WebpageDownloadJob,
                    'job_status',
                    job_status,
                    exact_match=True
                )
            else:
                jobs = SQLUtil.get_all_data(WebpageDownloadJob)

            # 对每个任务检查并更新状态
            updated_jobs = []
            for job in jobs:
                try:
                    # 查询该任务的详情
                    details = SQLUtil.query_by_column(
                        WebpageDownloadDetail,
                        'job_id',
                        job.job_id,
                        exact_match=True
                    )

                    # 检查并更新状态
                    status_updated = WebpageStatusManager.check_and_update_job_status(job, details)

                    # 如果状态有更新，保存到数据库
                    if status_updated:
                        SQLUtil.update_by_id(WebpageDownloadJob, job.job_id, {
                            'job_status': job.job_status,
                            'success_count': job.success_count,
                            'failed_count': job.failed_count,
                            'end_time': job.end_time
                        })

                        # 更新详情记录
                        for detail in details:
                            SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                                'download_status': detail.download_status,
                                'error_message': detail.error_message
                            })

                    updated_jobs.append(job)

                except Exception as job_update_error:
                    LogUtil.error(f"更新任务状态失败: job_id={job.job_id}, error={str(job_update_error)}")
                    # 继续处理其他任务
                    updated_jobs.append(job)

            jobs = updated_jobs
            
            # 按创建时间倒序排序
            jobs = sorted(jobs, key=lambda x: x.create_time, reverse=True)
            
            # 转换为字典列表
            job_list = []
            for job in jobs:
                job_dict = {
                    "job_id": job.job_id,
                    "job_name": job.job_name,
                    "job_status": job.job_status,
                    "total_count": job.total_count,
                    "success_count": job.success_count,
                    "failed_count": job.failed_count,
                    "create_time": job.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "start_time": job.start_time.strftime('%Y-%m-%d %H:%M:%S') if job.start_time else None,
                    "end_time": job.end_time.strftime('%Y-%m-%d %H:%M:%S') if job.end_time else None,
                    "update_time": job.update_time.strftime('%Y-%m-%d %H:%M:%S')
                }
                job_list.append(job_dict)
            
            # 分页处理
            paginated_result = PageUtil.paginate_list(job_list, page, page_size)
            
            LogUtil.info(f"查询任务列表成功: page={page}, page_size={page_size}, job_status={job_status}")
            return paginated_result
            
        except Exception as e:
            error_msg = f"查询任务列表失败: error={str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            try:
                SQLUtil.close()
            except:
                pass
