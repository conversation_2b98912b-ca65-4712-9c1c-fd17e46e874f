#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 17:00:00
# <AUTHOR> Assistant
# @File         : webpage_storage_service.py
# @Description  : 网页文件入库服务类
"""

import os
import json
import threading
from datetime import datetime
from typing import List, Dict, Any, Union
from utils.sql_util import SQLUtil
from utils.log_util import LogUtil
from utils.mongodb_util import MongodbUtil
from utils.minio_util import MinIoUtil
from service_crawer_manage.entity.webpage_entity import WebpageDownloadDetail, DownloadStatus
import hashlib

class WebpageStorageService:
    """网页文件入库服务类"""
    
    @staticmethod
    def store_files_by_job_id(job_id: str) -> Dict[str, Any]:
        """
        通过job_id批量入库文件
        
        Args:
            job_id: 任务ID
            
        Returns:
            入库结果
        """
        try:
            SQLUtil.connect()
            
            # 获取该任务的所有成功下载记录
            details = SQLUtil.query_by_multiple_columns(
                WebpageDownloadDetail,
                {
                    'job_id': job_id,
                    'download_status': DownloadStatus.SUCCESS.value
                }
            )
            
            if not details:
                return {
                    "success": True,
                    "message": f"任务 {job_id} 没有可入库的文件",
                    "store_count": 0
                }
            
            # 提取详情ID列表，避免跨线程传递ORM对象
            detail_ids = [detail.id for detail in details]

            # 启动后台入库任务
            WebpageStorageService._start_background_storage(detail_ids)
            
            LogUtil.info(f"文件入库任务启动成功: job_id={job_id}, 共 {len(details)} 个文件待入库")
            
            return {
                "success": True,
                "message": f"文件入库任务启动成功，共 {len(details)} 个文件待入库",
                "store_count": len(details)
            }
            
        except Exception as e:
            error_msg = f"启动文件入库失败: job_id={job_id}, error={str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            try:
                SQLUtil.close()
            except:
                pass
    
    @staticmethod
    def store_files_by_urls_and_names(url_name_pairs: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        通过网页地址+保存名称入库文件
        
        Args:
            url_name_pairs: 网页地址和名称对列表，格式: [{"webpage_url": "xxx", "webpage_name": "xxx"}]
            
        Returns:
            入库结果
        """
        try:
            SQLUtil.connect()
            
            # 获取需要入库的记录
            store_details = []
            for pair in url_name_pairs:
                webpage_url = pair.get('webpage_url')
                webpage_name = pair.get('webpage_name')
                
                if not webpage_url or not webpage_name:
                    continue
                
                details = SQLUtil.query_by_multiple_columns(
                    WebpageDownloadDetail,
                    {
                        'webpage_url': webpage_url,
                        'webpage_name': webpage_name,
                        'download_status': DownloadStatus.SUCCESS.value
                    }
                )
                
                if details:
                    # 选择最新的成功记录
                    latest_detail = max(details, key=lambda x: x.download_time or x.create_time)
                    store_details.append(latest_detail)
            
            if not store_details:
                return {
                    "success": True,
                    "message": "没有找到可入库的文件",
                    "store_count": 0
                }
            
            # 提取详情ID列表，避免跨线程传递ORM对象
            detail_ids = [detail.id for detail in store_details]

            # 启动后台入库任务
            WebpageStorageService._start_background_storage(detail_ids)
            
            LogUtil.info(f"文件入库任务启动成功，共 {len(store_details)} 个文件待入库")
            
            return {
                "success": True,
                "message": f"文件入库任务启动成功，共 {len(store_details)} 个文件待入库",
                "store_count": len(store_details)
            }
            
        except Exception as e:
            error_msg = f"启动文件入库失败: error={str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            try:
                SQLUtil.close()
            except:
                pass
    
    @staticmethod
    def _start_background_storage(detail_ids: List[int]):
        """启动后台入库任务"""
        def run_storage():
            try:
                WebpageStorageService._execute_storage_task(detail_ids)
            except Exception as e:
                LogUtil.error(f"后台入库任务执行失败: error={str(e)}")

        # 启动后台线程
        storage_thread = threading.Thread(target=run_storage, daemon=True)
        storage_thread.start()
        LogUtil.info(f"后台入库任务已启动，共 {len(detail_ids)} 个文件")
    
    @staticmethod
    def _execute_storage_task(detail_ids: List[int]):
        """执行入库任务"""
        try:
            # 连接数据库和存储服务
            SQLUtil.connect()
            MongodbUtil.connect()
            MinIoUtil.connect()

            success_count = 0
            failed_count = 0

            # 创建临时JSON目录
            temp_json_dir = "./temp_generated_jsons"
            os.makedirs(temp_json_dir, exist_ok=True)

            for detail_id in detail_ids:
                # 重新查询详情记录，避免会话绑定问题
                detail = SQLUtil.get_data_by_id(WebpageDownloadDetail, detail_id)
                if not detail:
                    LogUtil.warning(f"详情记录不存在: detail_id={detail_id}")
                    failed_count += 1
                    continue
                try:
                    # 检查文件是否存在
                    if not detail.file_path or not os.path.exists(detail.file_path):
                        LogUtil.error(f"文件不存在，跳过入库: {detail.webpage_name}")
                        failed_count += 1
                        continue
                    
                    # 生成JSON元数据
                    json_data = WebpageStorageService._generate_json_metadata(detail)
                    
                    # 保存JSON文件
                    json_filename = f"{detail.webpage_name}_{detail.id}.json"
                    json_filepath = os.path.join(temp_json_dir, json_filename)
                    
                    with open(json_filepath, 'w', encoding='utf-8') as f:
                        json.dump(json_data, f, ensure_ascii=False, indent=2)
                    
                    # 使用现有的上传脚本进行入库
                    from service_crawer_manage.script.upload_files_and_infos import process_data
                    
                    # 创建临时目录结构
                    temp_pdf_dir = os.path.dirname(detail.file_path)
                    
                    # 调用入库函数
                    process_data(temp_pdf_dir, temp_json_dir)
                    
                    # 更新状态为已入库
                    SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                        'download_status': DownloadStatus.STORED.value
                    })
                    
                    success_count += 1
                    LogUtil.info(f"文件入库成功: {detail.webpage_name}")
                    
                    # 清理临时JSON文件
                    if os.path.exists(json_filepath):
                        os.remove(json_filepath)
                        
                except Exception as e:
                    failed_count += 1
                    LogUtil.error(f"文件入库失败: {detail.webpage_name}, error={str(e)}")
            
            # 清理临时目录
            try:
                if os.path.exists(temp_json_dir):
                    os.rmdir(temp_json_dir)
            except:
                pass
            
            LogUtil.info(f"入库任务完成: 成功={success_count}, 失败={failed_count}")
            
        except Exception as e:
            LogUtil.error(f"执行入库任务失败: error={str(e)}")
        finally:
            try:
                SQLUtil.close()
            except:
                pass
    
    @staticmethod
    def _generate_json_metadata(detail: WebpageDownloadDetail) -> Dict[str, Any]:
        """生成JSON元数据"""
            
        mongodb_id = hashlib.md5((detail.webpage_url + detail.webpage_name).encode(encoding='UTF-8')).hexdigest()
        
        # 根据数据类型确定MongoDB集合
        collection_mapping = {
            "资讯": "news_label_info_new",
            "政策": "policy_label_info_new",
            "公告": "notice_label_info_new",
            "海关": "customs_label_info_new",
            "发票": "invoice_label_info_new",
            "研报": "research_report_label_info_new"
        }
        
        collection_name = collection_mapping.get(detail.data_type, "news_label_info_new")
        
        # 构建MinIO路径
        filename = f"{detail.webpage_name}.pdf"
        minio_path = f"/{detail.data_type.lower()}/{detail.download_time.year}/{detail.download_time.month}/{detail.download_time.day}/{filename}"
        
        # 生成元数据
        generated_json = {
            "_id": mongodb_id,
            "data": {
                "data_type": detail.data_type,
                "data_source": "手动上传"
            },
            "file": {
                "file_title": detail.webpage_name,
                "file_type": "pdf",
                "file_url": detail.webpage_url,
                "file_flag": {}
            },
            "time": {
                "release_time": detail.release_time.strftime("%Y-%m-%d %H:%M:%S") if detail.release_time else datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "crawling_time": detail.download_time.strftime("%Y-%m-%d %H:%M:%S") if detail.download_time else datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "parse_time": None
            },
            "minio": {
                "minio_name": "tiance-industry-finance",
                "minio_document_path": [minio_path]
            },
            "milvus": {
                "milvus_db_name": None,
                "milvus_collection_name": None
            },
            "status": 1
        }
        
        return {
            "mongodb_collection_name": collection_name,
            "generated_json": generated_json
        }
