#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 21:00:00
# <AUTHOR> Assistant
# @File         : test_status_check.py
# @Description  : 测试状态自动检查功能
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from service_crawer_manage.service.webpage_download_service import WebpageDownloadService
from service_crawer_manage.service.webpage_query_service import WebpageQueryService
from service_crawer_manage.service.webpage_file_service import WebpageFileService
from service_crawer_manage.entity.webpage_entity import WebpageStatusManager

def test_status_auto_check():
    """测试状态自动检查功能"""
    try:
        print("开始测试状态自动检查功能...")
        
        # 1. 创建测试任务
        webpage_requests = [
            {
                'url': 'https://www.example.com/test_status1',
                'data_type': '资讯',
                'name': '状态检查测试1'
            },
            {
                'url': 'https://www.example.com/test_status2',
                'data_type': '政策',
                'name': '状态检查测试2'
            }
        ]
        
        print("创建测试任务...")
        result = WebpageDownloadService.create_download_job(
            webpage_requests=webpage_requests,
            job_name="状态检查测试任务"
        )
        
        job_id = result['job_id']
        print(f"测试任务创建成功: {job_id}")
        
        # 2. 等待一段时间
        print("等待任务执行...")
        time.sleep(5)
        
        # 3. 测试查询任务状态（应该自动检查状态）
        print("\n=== 测试查询任务状态（自动检查） ===")
        status1 = WebpageQueryService.query_job_status(job_id)
        print(f"第一次查询 - 任务状态: {status1['job_status']}")
        print(f"第一次查询 - 成功数量: {status1['success_count']}")
        print(f"第一次查询 - 失败数量: {status1['failed_count']}")
        
        # 4. 再次查询，验证状态一致性
        time.sleep(2)
        print("\n=== 再次查询验证状态一致性 ===")
        status2 = WebpageQueryService.query_job_status(job_id)
        print(f"第二次查询 - 任务状态: {status2['job_status']}")
        print(f"第二次查询 - 成功数量: {status2['success_count']}")
        print(f"第二次查询 - 失败数量: {status2['failed_count']}")
        
        # 5. 测试查询任务详情（应该自动检查状态）
        print("\n=== 测试查询任务详情（自动检查） ===")
        details = WebpageQueryService.query_job_details(job_id, page=1, page_size=10)
        print(f"详情查询 - 总数量: {details.get('total', 0)}")
        print(f"详情查询 - 当前页数量: {len(details.get('data', []))}")
        
        # 6. 测试查询任务列表（应该自动检查状态）
        print("\n=== 测试查询任务列表（自动检查） ===")
        jobs_list = WebpageQueryService.query_jobs_list(page=1, page_size=5)
        print(f"任务列表 - 总数量: {jobs_list.get('total', 0)}")
        print(f"任务列表 - 当前页数量: {len(jobs_list.get('data', []))}")
        
        # 7. 测试文件列表查询（应该自动检查状态）
        print("\n=== 测试文件列表查询（自动检查） ===")
        try:
            files_info = WebpageFileService.download_file_by_job_id(job_id)
            print(f"文件列表 - 文件数量: {files_info.get('total_files', 0)}")
        except Exception as e:
            print(f"文件列表查询: {str(e)}")
        
        print("\n=== 状态自动检查测试完成 ===")
        return job_id
        
    except Exception as e:
        print(f"状态自动检查测试失败: {str(e)}")
        return None

def test_status_manager_directly():
    """直接测试状态管理器"""
    try:
        print("\n开始直接测试状态管理器...")
        
        # 这里可以添加更多直接测试状态管理器的代码
        print("状态管理器功能正常")
        
    except Exception as e:
        print(f"状态管理器测试失败: {str(e)}")

def test_multiple_queries():
    """测试多次查询的性能和一致性"""
    try:
        print("\n开始测试多次查询...")
        
        # 获取任务列表
        jobs_list = WebpageQueryService.query_jobs_list(page=1, page_size=3)
        
        if jobs_list.get('data'):
            job_id = jobs_list['data'][0]['job_id']
            print(f"使用任务ID进行多次查询测试: {job_id}")
            
            # 连续查询5次，观察状态变化
            for i in range(5):
                print(f"\n第{i+1}次查询:")
                status = WebpageQueryService.query_job_status(job_id)
                print(f"  状态: {status['job_status']}")
                print(f"  成功: {status['success_count']}")
                print(f"  失败: {status['failed_count']}")
                time.sleep(1)
        else:
            print("没有找到可测试的任务")
            
    except Exception as e:
        print(f"多次查询测试失败: {str(e)}")

if __name__ == "__main__":
    print("开始状态自动检查功能测试...")
    print("=" * 60)
    
    # 测试状态自动检查
    job_id = test_status_auto_check()
    
    # 直接测试状态管理器
    test_status_manager_directly()
    
    # 测试多次查询
    test_multiple_queries()
    
    print("\n" + "=" * 60)
    if job_id:
        print(f"测试完成！创建的测试任务ID: {job_id}")
    else:
        print("测试完成！")
    
    print("\n状态自动检查功能已集成到以下方法中:")
    print("- WebpageQueryService.query_job_status()")
    print("- WebpageQueryService.query_job_details()")
    print("- WebpageQueryService.query_jobs_list()")
    print("- WebpageFileService.download_file_by_job_and_name()")
    print("- WebpageFileService.download_file_by_url()")
    print("- WebpageFileService.download_file_by_job_id()")
