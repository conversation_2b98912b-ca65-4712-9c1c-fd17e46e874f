#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 19:00:00
# <AUTHOR> Assistant
# @File         : test_file_download.py
# @Description  : 测试文件下载功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from service_crawer_manage.service.webpage_file_service import WebpageFileService

def test_filename_sanitization():
    """测试文件名安全化"""
    try:
        # 测试各种文件名
        test_filenames = [
            "测试文件.pdf",
            "农业部文件_政策解读.pdf",
            "P020200520541415122780.pdf",
            "中文文档_2024年度报告.pdf",
            "包含<>:\"/\\|?*特殊字符的文件.pdf",
            "广东省2020年水产绿色健康养殖\"五大行动\"实施方案.pdf",
            "",  # 空文件名
            "a" * 300 + ".pdf"  # 超长文件名
        ]

        print("测试文件名安全化:")
        print("-" * 60)

        for filename in test_filenames:
            try:
                safe_filename = WebpageFileService._sanitize_filename(filename)
                print(f"原始文件名: {filename}")
                print(f"安全文件名: {safe_filename}")
                print(f"长度: {len(safe_filename)}")
                print("-" * 40)

            except Exception as e:
                print(f"安全化失败: {filename}, 错误: {str(e)}")
                print("-" * 40)

        print("测试完成！")

    except Exception as e:
        print(f"测试失败: {str(e)}")

def test_file_response():
    """测试文件响应创建"""
    try:
        # 测试包含中文的文件名
        test_cases = [
            ("test.pdf", "测试文件.pdf"),
            ("test2.pdf", "农业部文件_政策解读.pdf"),
            ("test3.pdf", "P020200520541415122780.pdf"),
            ("test4.pdf", "中文文档_2024年度报告.pdf")
        ]

        print("测试文件响应创建:")
        print("-" * 50)

        for file_path, filename in test_cases:
            try:
                # 先安全化文件名
                safe_filename = WebpageFileService._sanitize_filename(filename)

                # 创建文件响应
                response = WebpageFileService.get_file_response(file_path, safe_filename)

                print(f"文件路径: {file_path}")
                print(f"原始文件名: {filename}")
                print(f"安全文件名: {safe_filename}")
                print(f"媒体类型: {response.media_type}")
                print(f"响应头: {response.headers}")
                print("-" * 30)

            except Exception as e:
                print(f"创建响应失败: {filename}, 错误: {str(e)}")
                print("-" * 30)

        print("测试完成！")

    except Exception as e:
        print(f"测试失败: {str(e)}")

if __name__ == "__main__":
    print("开始测试文件名安全化...")
    test_filename_sanitization()

    print("\n" + "="*60 + "\n")

    print("开始测试文件响应创建...")
    test_file_response()
