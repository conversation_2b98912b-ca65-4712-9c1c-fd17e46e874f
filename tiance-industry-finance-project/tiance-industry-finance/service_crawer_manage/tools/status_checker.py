#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 21:30:00
# <AUTHOR> Assistant
# @File         : status_checker.py
# @Description  : 状态检查工具，可手动触发全量状态检查
"""

import sys
import os
import argparse
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from utils.sql_util import SQLUtil
from utils.log_util import LogUtil
from service_crawer_manage.entity.webpage_entity import (
    WebpageDownloadJob, WebpageDownloadDetail, WebpageStatusManager
)

class StatusChecker:
    """状态检查工具类"""
    
    @staticmethod
    def check_all_jobs():
        """检查所有任务的状态"""
        try:
            SQLUtil.connect()
            
            # 获取所有任务
            jobs = SQLUtil.get_all_data(WebpageDownloadJob)
            
            print(f"开始检查 {len(jobs)} 个任务的状态...")
            
            updated_jobs = 0
            updated_details = 0
            
            for job in jobs:
                try:
                    # 获取任务的详情
                    details = SQLUtil.query_by_column(
                        WebpageDownloadDetail,
                        'job_id',
                        job.job_id,
                        exact_match=True
                    )
                    
                    # 检查并更新状态
                    status_updated = WebpageStatusManager.check_and_update_job_status(job, details)
                    
                    if status_updated:
                        # 保存任务状态更新
                        SQLUtil.update_by_id(WebpageDownloadJob, job.job_id, {
                            'job_status': job.job_status,
                            'success_count': job.success_count,
                            'failed_count': job.failed_count,
                            'end_time': job.end_time
                        })
                        
                        # 保存详情状态更新
                        detail_updates = 0
                        for detail in details:
                            try:
                                SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                                    'download_status': detail.download_status,
                                    'error_message': detail.error_message
                                })
                                detail_updates += 1
                            except Exception as detail_error:
                                LogUtil.error(f"更新详情失败: detail_id={detail.id}, error={str(detail_error)}")
                        
                        updated_jobs += 1
                        updated_details += detail_updates
                        
                        print(f"✅ 任务 {job.job_id} 状态已更新: {job.job_status}")
                    else:
                        print(f"⏸️  任务 {job.job_id} 状态无变化: {job.job_status}")
                        
                except Exception as job_error:
                    print(f"❌ 检查任务 {job.job_id} 失败: {str(job_error)}")
                    LogUtil.error(f"检查任务状态失败: job_id={job.job_id}, error={str(job_error)}")
            
            print(f"\n状态检查完成:")
            print(f"- 总任务数: {len(jobs)}")
            print(f"- 更新的任务数: {updated_jobs}")
            print(f"- 更新的详情数: {updated_details}")
            
            return updated_jobs, updated_details
            
        except Exception as e:
            print(f"状态检查失败: {str(e)}")
            LogUtil.error(f"全量状态检查失败: error={str(e)}")
            return 0, 0
        finally:
            try:
                SQLUtil.close()
            except:
                pass
    
    @staticmethod
    def check_specific_job(job_id: str):
        """检查特定任务的状态"""
        try:
            SQLUtil.connect()
            
            # 获取任务
            job = SQLUtil.get_data_by_id(WebpageDownloadJob, job_id)
            if not job:
                print(f"❌ 任务不存在: {job_id}")
                return False
            
            print(f"检查任务: {job_id}")
            print(f"当前状态: {job.job_status}")
            print(f"成功数量: {job.success_count}")
            print(f"失败数量: {job.failed_count}")
            
            # 获取详情
            details = SQLUtil.query_by_column(
                WebpageDownloadDetail,
                'job_id',
                job_id,
                exact_match=True
            )
            
            print(f"详情记录数: {len(details)}")
            
            # 检查状态
            status_updated = WebpageStatusManager.check_and_update_job_status(job, details)
            
            if status_updated:
                # 保存更新
                SQLUtil.update_by_id(WebpageDownloadJob, job_id, {
                    'job_status': job.job_status,
                    'success_count': job.success_count,
                    'failed_count': job.failed_count,
                    'end_time': job.end_time
                })
                
                for detail in details:
                    SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                        'download_status': detail.download_status,
                        'error_message': detail.error_message
                    })
                
                print(f"✅ 状态已更新:")
                print(f"新状态: {job.job_status}")
                print(f"新成功数量: {job.success_count}")
                print(f"新失败数量: {job.failed_count}")
                
                return True
            else:
                print(f"⏸️  状态无变化")
                return False
                
        except Exception as e:
            print(f"❌ 检查特定任务失败: {str(e)}")
            LogUtil.error(f"检查特定任务失败: job_id={job_id}, error={str(e)}")
            return False
        finally:
            try:
                SQLUtil.close()
            except:
                pass
    
    @staticmethod
    def show_status_summary():
        """显示状态统计摘要"""
        try:
            SQLUtil.connect()
            
            # 统计任务状态
            jobs = SQLUtil.get_all_data(WebpageDownloadJob)
            job_status_count = {}
            for job in jobs:
                status = job.job_status
                job_status_count[status] = job_status_count.get(status, 0) + 1
            
            # 统计详情状态
            details = SQLUtil.get_all_data(WebpageDownloadDetail)
            detail_status_count = {}
            for detail in details:
                status = detail.download_status
                detail_status_count[status] = detail_status_count.get(status, 0) + 1
            
            print("📊 状态统计摘要:")
            print(f"总任务数: {len(jobs)}")
            print("任务状态分布:")
            for status, count in job_status_count.items():
                print(f"  {status}: {count}")
            
            print(f"\n总详情数: {len(details)}")
            print("详情状态分布:")
            for status, count in detail_status_count.items():
                print(f"  {status}: {count}")
                
        except Exception as e:
            print(f"❌ 获取状态摘要失败: {str(e)}")
        finally:
            try:
                SQLUtil.close()
            except:
                pass

def main():
    parser = argparse.ArgumentParser(description='网页下载状态检查工具')
    parser.add_argument('--all', action='store_true', help='检查所有任务状态')
    parser.add_argument('--job-id', type=str, help='检查特定任务状态')
    parser.add_argument('--summary', action='store_true', help='显示状态统计摘要')
    
    args = parser.parse_args()
    
    print(f"🔍 网页下载状态检查工具")
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    if args.all:
        print("执行全量状态检查...")
        StatusChecker.check_all_jobs()
    elif args.job_id:
        print(f"检查特定任务: {args.job_id}")
        StatusChecker.check_specific_job(args.job_id)
    elif args.summary:
        print("显示状态统计摘要...")
        StatusChecker.show_status_summary()
    else:
        print("请指定操作参数:")
        print("  --all        检查所有任务状态")
        print("  --job-id ID  检查特定任务状态")
        print("  --summary    显示状态统计摘要")
        print("\n示例:")
        print("  python status_checker.py --all")
        print("  python status_checker.py --job-id abc123")
        print("  python status_checker.py --summary")

if __name__ == "__main__":
    main()
