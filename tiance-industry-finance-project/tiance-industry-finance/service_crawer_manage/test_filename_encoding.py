#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 18:30:00
# <AUTHOR> Assistant
# @File         : test_filename_encoding.py
# @Description  : 测试文件名编码
"""

from urllib.parse import quote

def test_filename_encoding():
    """测试文件名编码"""
    
    # 测试包含中文的文件名
    test_filenames = [
        "测试文件.pdf",
        "中文文档_2024.pdf",
        "P020200520541415122780.pdf",
        "农业部文件_政策解读.pdf"
    ]
    
    print("测试文件名编码:")
    print("-" * 50)
    
    for filename in test_filenames:
        encoded = quote(filename, safe='')
        header_value = f"attachment; filename*=UTF-8''{encoded}"
        
        print(f"原始文件名: {filename}")
        print(f"编码后: {encoded}")
        print(f"Content-Disposition: {header_value}")
        print("-" * 30)

if __name__ == "__main__":
    test_filename_encoding()
