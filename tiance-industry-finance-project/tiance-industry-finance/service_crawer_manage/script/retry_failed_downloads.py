#!/usr/bin/env python3
import asyncio
from pyppeteer import launch
import os
import time
import requests
import re
import logging
from urllib.parse import urljoin, urlparse

# 设置日志格式
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def download_with_fallback_strategy(title, url, output_dir):
    """使用多种策略尝试下载"""
    pdf_filename = os.path.join(output_dir, f"{title}.pdf")
    
    if os.path.exists(pdf_filename):
        logging.info(f"Skipping {pdf_filename}, already exists.")
        return True

    # 策略1: 直接下载PDF
    logging.info(f"using strategy 1")
    if url.lower().endswith(".pdf") or ".pdf?" in url.lower():
        if await try_direct_pdf_download(url, pdf_filename):
            return True
    
    # 策略2: 使用requests获取HTML然后查找PDF链接
    if await try_requests_based_download(url, pdf_filename, title, output_dir):
        return True
    
    logging.info(f"using strategy 3")
    # 策略3: 使用浏览器但采用更宽松的策略
    if await try_browser_download_relaxed(url, pdf_filename, title, output_dir):
        return True
    
    return False

async def try_direct_pdf_download(url, pdf_filename):
    """尝试直接下载PDF"""
    try:
        logging.info(f"Attempting direct PDF download: {url}")
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/pdf,application/octet-stream,*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': url,
        }
        
        response = requests.get(url, stream=True, timeout=30, headers=headers, allow_redirects=True)
        response.raise_for_status()
        
        content_type = response.headers.get('Content-Type', '').lower()
        if 'pdf' in content_type or 'octet-stream' in content_type:
            with open(pdf_filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            logging.info(f"Successfully downloaded direct PDF: {pdf_filename}")
            return True
        else:
            logging.warning(f"URL does not return PDF content, Content-Type: {content_type}")
            return False
            
    except Exception as e:
        logging.error(f"Direct PDF download failed: {e}")
        return False

async def try_direct_pdf_download_with_validation(url, pdf_filename, expected_title):
    """尝试直接下载PDF并验证内容"""
    try:
        logging.info(f"Attempting validated PDF download: {url}")
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/pdf,application/octet-stream,*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': url,
        }

        response = requests.get(url, stream=True, timeout=30, headers=headers, allow_redirects=True)
        response.raise_for_status()

        content_type = response.headers.get('Content-Type', '').lower()
        content_length = response.headers.get('Content-Length', '0')

        # 检查内容类型
        if not ('pdf' in content_type or 'octet-stream' in content_type):
            logging.warning(f"URL does not return PDF content, Content-Type: {content_type}")
            return False

        # 检查文件大小（太小可能是错误页面）
        if content_length.isdigit() and int(content_length) < 1000:
            logging.warning(f"PDF file too small: {content_length} bytes")
            return False

        # 下载文件
        temp_filename = pdf_filename + '.tmp'
        with open(temp_filename, 'wb') as f:
            downloaded_size = 0
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
                downloaded_size += len(chunk)

        # 验证下载的文件
        if downloaded_size < 1000:
            logging.warning(f"Downloaded PDF too small: {downloaded_size} bytes")
            os.remove(temp_filename)
            return False

        # 重命名为最终文件名
        os.rename(temp_filename, pdf_filename)
        logging.info(f"Successfully downloaded and validated PDF: {pdf_filename} ({downloaded_size} bytes)")
        return True

    except Exception as e:
        logging.error(f"Validated PDF download failed: {e}")
        # 清理临时文件
        temp_filename = pdf_filename + '.tmp'
        if os.path.exists(temp_filename):
            os.remove(temp_filename)
        return False

async def try_requests_based_download(url, pdf_filename, title, output_dir):
    """使用requests获取页面内容并查找PDF链接"""
    try:
        logging.info(f"Attempting requests-based download: {url}")
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }
        
        response = requests.get(url, timeout=15, headers=headers)
        response.raise_for_status()
        
        # 查找页面中的PDF链接，使用更严格的匹配

        # 更严格的PDF链接匹配模式
        pdf_patterns = [
            r'href=["\']([^"\']*\.pdf(?:\?[^"\']*)?)["\']',  # 标准PDF链接
            r'href=["\']([^"\']*\.PDF(?:\?[^"\']*)?)["\']',  # 大写PDF
            r'data-url=["\']([^"\']*\.pdf(?:\?[^"\']*)?)["\']',  # data-url属性
            r'src=["\']([^"\']*\.pdf(?:\?[^"\']*)?)["\']',   # src属性
        ]

        pdf_links = []
        for pattern in pdf_patterns:
            links = re.findall(pattern, response.text, re.IGNORECASE)
            pdf_links.extend(links)

        # 去重并过滤
        pdf_links = list(set(pdf_links))

        # 过滤掉明显不相关的PDF链接
        filtered_links = []
        for link in pdf_links:
            # 跳过明显的广告、图标等
            if any(skip_word in link.lower() for skip_word in ['icon', 'logo', 'banner', 'ad', 'advertisement']):
                continue
            # 跳过太短的链接（可能是相对路径错误）
            if len(link) < 10:
                continue
            filtered_links.append(link)

        if filtered_links:
            logging.info(f"Found {len(filtered_links)} potential PDF links")
            for i, pdf_link in enumerate(filtered_links):
                # 转换为绝对URL
                absolute_url = urljoin(url, pdf_link)
                logging.info(f"Trying PDF link {i+1}/{len(filtered_links)}: {absolute_url}")

                # 尝试下载并验证
                if await try_direct_pdf_download_with_validation(absolute_url, pdf_filename, title):
                    return True

        # 如果没有找到PDF链接，不尝试保存HTML
        logging.info("No valid PDF links found")
        return False
        
    except Exception as e:
        logging.error(f"Requests-based download failed: {e}")
        return False

async def try_browser_download_relaxed(url, pdf_filename, title, output_dir):
    """使用浏览器但采用更宽松的策略"""
    browser = None
    try:
        logging.info(f"Attempting relaxed browser download: {url}")
        
        browser = await launch(headless=True, args=[
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu',
            '--hide-scrollbars',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding'
        ])
        
        page = await browser.newPage()
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        await page.setViewport({'width': 1200, 'height': 800})
        
        # 简单加载策略：只等待DOM加载完成
        try:
            await page.goto(url, {'waitUntil': 'domcontentloaded', 'timeout': 20000})
            logging.info("Page loaded with domcontentloaded")
        except:
            try:
                await page.goto(url, {'timeout': 10000})
                logging.info("Page loaded with minimal timeout")
            except Exception as e:
                logging.error(f"Failed to load page: {e}")
                return False
        
        # 等待页面稳定
        await asyncio.sleep(3)
        
        # 尝试生成PDF
        await page.pdf({
            'path': pdf_filename,
            'format': 'A4',
            'printBackground': True,
            'margin': {
                'top': '20mm',
                'right': '10mm',
                'bottom': '20mm',
                'left': '10mm'
            }
        })
        
        if os.path.exists(pdf_filename) and os.path.getsize(pdf_filename) > 1000:
            logging.info(f"Successfully saved relaxed browser PDF: {pdf_filename}")
            return True
        else:
            logging.warning("PDF generation failed or file too small")
            return False
            
    except Exception as e:
        logging.error(f"Relaxed browser download failed: {e}")
        return False
    finally:
        if browser:
            await browser.close()

async def main():
    output_directory = "./downloaded_pdfs"
    failed_file = "./downloaded_pdfs/failed_downloads.txt"
    
    if not os.path.exists(output_directory):
        os.makedirs(output_directory)
    
    if not os.path.exists(failed_file):
        logging.error(f"Failed downloads file not found: {failed_file}")
        return
    
    # 读取失败的下载
    failed_urls = []
    with open(failed_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and ',' in line:
                
                parts = line.split(',', 2)
                print(parts)
                if len(parts) >= 3:
                    title, url, datatype = parts[0], parts[1],parts[2]
                    failed_urls.append((title, url))
    
    logging.info(f"Found {len(failed_urls)} failed downloads to retry")
    
    success_count = 0
    still_failed = []
    
    for i, (title, url) in enumerate(failed_urls, 1):
        logging.info(f"\n{'='*60}")
        logging.info(f"Retrying {i}/{len(failed_urls)}: {title}")
        logging.info(f"URL: {url}")
        logging.info(f"{'='*60}")
        
        # 清理标题中的特殊字符
        safe_title = re.sub(r'[^\w\-_\u4e00-\u9fff]', '', title)
        
        success = await download_with_fallback_strategy(safe_title, url, output_directory)
        
        if success:
            success_count += 1
            logging.info(f"✓ Successfully downloaded: {title}")
        else:
            still_failed.append((title, url))
            logging.error(f"✗ Still failed: {title}")
        
        # 请求间隔
        time.sleep(2)
    
    # 更新失败列表
    if still_failed:
        with open(failed_file, 'w', encoding='utf-8') as f:
            for title, url in still_failed:
                f.write(f"{title},{url},None\n")
        logging.info(f"\n{len(still_failed)} downloads still failed, updated failed_downloads.txt")
    else:
        # 如果全部成功，删除失败文件
        # os.remove(failed_file)
        logging.info("\nAll failed downloads completed successfully!")
    
    logging.info(f"\nRetry Summary:")
    logging.info(f"  Total retried: {len(failed_urls)}")
    logging.info(f"  Successful: {success_count}")
    logging.info(f"  Still failed: {len(still_failed)}")

if __name__ == '__main__':
    asyncio.run(main())
