#!/usr/bin/env python
# -*- coding: utf-8 -*-
import argparse
import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--name', required=True)
    parser.add_argument('--url', required=True)
    parser.add_argument('--dir', required=True)
    args = parser.parse_args()

    try:
        from service_crawer_manage.script.download_pdfs import download_and_convert_to_pdf_pyppeteer
        success = await download_and_convert_to_pdf_pyppeteer(args.name, args.url, args.dir)
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
