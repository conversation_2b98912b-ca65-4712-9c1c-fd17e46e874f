﻿import asyncio
from pyppeteer import launch
import os
import time
import requests
import re
import logging
from urllib.parse import urljoin, urlparse

# 设置日志格式
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def is_valid_url(url):
    """检查URL是否有效"""
    parsed = urlparse(url)
    return bool(parsed.netloc) and bool(parsed.scheme)

def is_problematic_url(url):
    """检查是否是已知有问题的URL"""
    problematic_domains = [
        'stcn.com',  # 证券时报网 - PDF生成时死机
        'mp.weixin.qq.com',  # 微信公众号 - 反爬虫验证码
    ]

    for domain in problematic_domains:
        if domain in url:
            return True
    return False

async def download_pdf_attachments(page, base_url, output_dir, title):
    """下载当前页面中的PDF附件"""
    try:
        # 获取页面中所有的PDF链接
        pdf_links = await page.evaluate('''(baseUrl) => {
            const links = Array.from(document.querySelectorAll('a[href$=".pdf"], a[href$=".PDF"]'));
            const validLinks = [];
            
            for (const link of links) {
                try {
                    // 将相对路径转换为绝对路径
                    const absoluteUrl = new URL(link.href, baseUrl).href;
                    // 检查是否是PDF链接
                    if (absoluteUrl.toLowerCase().endsWith('.pdf')) {
                        validLinks.push(absoluteUrl);
                    }
                } catch (e) {
                    console.warn('Invalid URL found:', link.href);
                }
            }
            
            // 去重
            return Array.from(new Set(validLinks));
        }''', base_url)
        
        if not pdf_links:
            logging.info("No downloadable PDF links found on the page.")
            return
            
        logging.info(f"Found {len(pdf_links)} PDF links on the page")
        
        # 创建附件目录
        attachments_dir = os.path.join(output_dir, f"{title}_attachments")
        if not os.path.exists(attachments_dir):
            os.makedirs(attachments_dir)
        
        # 下载所有PDF附件
        for pdf_url in pdf_links:
            try:
                # 获取文件名
                parsed = urlparse(pdf_url)
                filename = os.path.basename(parsed.path)
                if not filename:
                    filename = f"attachment_{int(time.time())}.pdf"
                
                # 确保文件名以.pdf结尾
                if not filename.lower().endswith('.pdf'):
                    filename += '.pdf'
                
                safe_filename = re.sub(r'[^\w\-_.]', '', filename)
                pdf_path = os.path.join(attachments_dir, safe_filename)
                
                # 如果文件已存在则跳过
                if os.path.exists(pdf_path):
                    logging.info(f"Skipping existing attachment: {pdf_path}")
                    continue
                
                logging.info(f"Downloading PDF attachment: {pdf_url}")
                response = requests.get(pdf_url, stream=True, timeout=30)
                response.raise_for_status()
                
                with open(pdf_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                logging.info(f"Successfully downloaded attachment: {pdf_path}")
                
            except Exception as e:
                logging.error(f"Failed to download PDF attachment {pdf_url}: {str(e)}")
    
    except Exception as e:
        logging.error(f"Error while extracting PDF links: {str(e)}")

async def download_and_convert_to_pdf_pyppeteer(title, url, output_dir):
    pdf_filename = os.path.join(output_dir, f"{title}.pdf")
    if os.path.exists(pdf_filename):
        logging.info(f"Skipping {pdf_filename}, already exists.")
        return True

    # 直接下载PDF文件的逻辑
    if url.lower().endswith(".pdf") or ".pdf?" in url.lower():
        try:
            logging.info(f"Attempting direct PDF download: {url}")
            # 添加更完整的请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'application/pdf,application/octet-stream,*/*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
            response = requests.get(url, stream=True, timeout=30, headers=headers, allow_redirects=True)
            response.raise_for_status()

            # 检查响应内容类型
            content_type = response.headers.get('Content-Type', '').lower()
            if 'pdf' in content_type or 'octet-stream' in content_type:
                with open(pdf_filename, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                logging.info(f"Successfully downloaded direct PDF: {pdf_filename}")
                return True
            else:
                logging.warning(f"URL does not return PDF content, Content-Type: {content_type}")
        except requests.exceptions.RequestException as e:
            logging.error(f"Direct PDF download failed for {url}: {e}")

    browser = None
    try:
        logging.info(f"Attempting to download with Pyppeteer: {url}")
        browser = await launch(headless=True, args=[
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu',
            '--hide-scrollbars',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
        ])
        page = await browser.newPage()

        # 设置用户代理
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

        # 设置视口大小
        await page.setViewport({'width': 1920, 'height': 1080})

        # 暂时禁用请求拦截以避免协程问题
        # await page.setRequestInterception(True)

        # 添加打印拦截处理
        await page.exposeFunction('customPrintToPDF', lambda: pdf_filename)

        # 覆盖window.print方法
        await page.evaluate('''() => {
            window.print = function() {
                window.customPrintToPDF().then(filename => {
                    console.log('Print intercepted, PDF will be saved as:', filename);
                });
            };
        }''')

        # 设置视口大小
        await page.setViewport({'width': 1200, 'height': 800})

        # 尝试访问页面，使用更宽松的等待策略
        max_retries = 3
        page_loaded = False

        for attempt in range(max_retries):
            try:
                logging.info(f"Attempt {attempt + 1} to load page: {url}")

                # 尝试不同的等待策略
                wait_strategies = ['domcontentloaded', 'load', 'networkidle0']

                for strategy in wait_strategies:
                    try:
                        await page.goto(url, {'waitUntil': strategy, 'timeout': 30000})
                        logging.info(f"Page loaded successfully with strategy: {strategy}")
                        page_loaded = True
                        break
                    except Exception as e:
                        logging.warning(f"Strategy {strategy} failed: {e}")
                        continue

                if page_loaded:
                    break

            except Exception as e:
                logging.warning(f"Attempt {attempt + 1} failed to load {url}: {e}")
                if attempt == max_retries - 1:
                    # 最后一次尝试：直接加载不等待
                    try:
                        await page.goto(url, {'timeout': 15000})
                        logging.info("Page loaded with minimal timeout")
                        page_loaded = True
                    except:
                        logging.error(f"All attempts failed for {url}")
                        raise
                await asyncio.sleep(2)

        # 等待页面稳定
        if page_loaded:
            await asyncio.sleep(3)
        
        # 尝试查找打印按钮
        print_button_found = False
        handled_by_interceptor = False  # 标记是否被拦截器处理
        
        try:
            # 尝试多种方式查找打印按钮（添加超时控制）
            print_button = None
            try:
                print_button = await asyncio.wait_for(
                    page.querySelector('a[onclick*="print"], button[onclick*="print"], input[onclick*="print"]'),
                    timeout=10.0
                )
            except asyncio.TimeoutError:
                logging.warning("Print button querySelector timeout")

            if not print_button:
                try:
                    print_button = await asyncio.wait_for(page.xpath('''
                        //a[contains(translate(text(), 'PRINT', 'print'), 'print')] |
                        //button[contains(translate(text(), 'PRINT', 'print'), 'print')] |
                        //input[contains(translate(@value, 'PRINT', 'print'), 'print')] |
                        //a[contains(translate(text(), '打印', ''), '打印')] |
                        //button[contains(translate(text(), '打印', ''), '打印')] |
                        //input[contains(translate(@value, '打印', ''), '打印')]
                    '''), timeout=10.0)
                    if print_button:
                        print_button = print_button[0] if len(print_button) > 0 else None
                except asyncio.TimeoutError:
                    logging.warning("Print button xpath timeout")
                    print_button = None
            
            if print_button:
                logging.info("Found print button, attempting to click...")
                
                # 点击前保存原始页面内容（防止导航丢失）
                original_content = await page.content()
                
                # 添加点击事件监听器
                def handle_dialog(dialog):
                    logging.warning(f"Dialog message: {dialog.message}")
                    dialog.dismiss()

                page.on('dialog', handle_dialog)
                
                try:
                    # 尝试点击打印按钮
                    await print_button.click()
                    
                    # 检查是否触发了我们的打印拦截
                    await asyncio.sleep(3)  # 等待页面JS执行
                    
                    # 检查是否触发了拦截器
                    intercepted = await page.evaluate('''() => {
                        return window.printTriggered || false;
                    }''')
                    
                    if intercepted:
                        logging.info("Print intercepted, saving directly...")
                        print_button_found = True
                        handled_by_interceptor = True
                    else:
                        # 回退到原始方法
                        await page.setContent(original_content)
                        logging.warning("Print not intercepted, using fallback method")
                except Exception as e:
                    logging.error(f"Error clicking print button: {e}")
                    # 回退到原始内容
                    await page.setContent(original_content)
                
                # 移除对话框监听器
                page.removeAllListeners('dialog')
                
        except Exception as e:
            logging.error(f"Error handling print button: {e}")

        # 如果被拦截器处理，直接使用当前页面生成PDF
        if handled_by_interceptor:
            # 在生成PDF前可能需要滚动等待或布局调整
            await page.evaluate('''() => {
                window.scrollTo(0, 0);
                document.body.classList.add('print-preview');
            }''')
            await asyncio.sleep(1)
            
            # 移除不需要的元素
            await remove_unnecessary_elements(page)
            
            # 生成PDF（带超时控制）
            try:
                await asyncio.wait_for(page.pdf({
                    'path': pdf_filename,
                    'format': 'A4',
                    'printBackground': True,
                    'margin': {
                        'top': '20mm',
                        'right': '10mm',
                        'bottom': '20mm',
                        'left': '10mm'
                    }
                }), timeout=60.0)

                # 对于微信文章，生成PDF后检查内容
                if 'mp.weixin.qq.com' in url:
                    final_check = await page.evaluate('''() => {
                        const pageText = document.body.textContent || document.body.innerText || '';
                        const captchaKeywords = [
                            '验证码', '验证', 'captcha', 'verification',
                            '请输入验证码', '安全验证', '人机验证',
                            'wappoc_appmsgcaptcha', '微信安全验证'
                        ];

                        const hasCaptcha = captchaKeywords.some(keyword =>
                            pageText.toLowerCase().includes(keyword.toLowerCase())
                        );

                        return {
                            hasCaptcha: hasCaptcha,
                            textLength: pageText.length,
                            hasRealContent: pageText.length > 500 && !hasCaptcha
                        };
                    }''')

                    if final_check['hasCaptcha'] or not final_check['hasRealContent']:
                        logging.error(f"WeChat intercepted PDF contains verification content, deleting and marking as failed")
                        # 删除生成的无效PDF
                        if os.path.exists(pdf_filename):
                            os.remove(pdf_filename)
                        return False

                logging.info(f"Successfully saved intercepted PDF: {pdf_filename}")
            except asyncio.TimeoutError:
                logging.error(f"Intercepted PDF generation timeout for {pdf_filename}")
                return False
            
            # 恢复原始打印功能
            await page.evaluate('''() => {
                window.print = originalPrintFunction;
            }''')
        else:
            # 原始的非拦截处理流程（当打印按钮未被找到或未触发时）
            if not print_button_found:
                logging.info("Using fallback method to save page as PDF")
                
            # 在清理操作前进行滚动以确保图片加载（添加超时控制）
            try:
                await asyncio.wait_for(auto_scroll(page), timeout=30.0)
            except asyncio.TimeoutError:
                logging.warning("Auto scroll timeout, skipping")

            # 移除不需要的元素（添加超时控制）
            try:
                await asyncio.wait_for(remove_unnecessary_elements(page), timeout=15.0)
            except asyncio.TimeoutError:
                logging.warning("Remove elements timeout, skipping")

            # 针对微信公众号的特殊处理
            if 'mp.weixin.qq.com' in url:
                logging.info("Applying WeChat official account fixes...")
                try:
                    await asyncio.wait_for(fix_wechat_images(page), timeout=15.0)
                    await asyncio.sleep(3)  # 增加等待时间

                    # 检查页面内容是否正常加载，以及是否遇到验证码
                    content_check = await page.evaluate('''() => {
                        const content = document.querySelector('#js_content, .rich_media_content');
                        const title = document.querySelector('#activity-name, .rich_media_title, h1');
                        const pageText = document.body.textContent || document.body.innerText || '';

                        // 检查验证码相关关键词
                        const captchaKeywords = [
                            '验证码', '验证', 'captcha', 'verification',
                            '请输入验证码', '安全验证', '人机验证',
                            'wappoc_appmsgcaptcha', '微信安全验证',
                            '为了确认本次访问为正常用户行为',
                            '请点击完成验证', '滑动验证'
                        ];

                        const hasCaptcha = captchaKeywords.some(keyword =>
                            pageText.toLowerCase().includes(keyword.toLowerCase())
                        );

                        // 检查是否有验证码相关的元素
                        const captchaElements = document.querySelectorAll([
                            '.captcha', '.verification', '.verify',
                            '#captcha', '#verification', '#verify',
                            '[class*="captcha"]', '[id*="captcha"]',
                            '[class*="verify"]', '[id*="verify"]'
                        ].join(','));

                        return {
                            hasContent: content && content.textContent.length > 100,
                            hasTitle: title && title.textContent.length > 0,
                            contentLength: content ? content.textContent.length : 0,
                            hasCaptcha: hasCaptcha || captchaElements.length > 0,
                            pageTextLength: pageText.length,
                            url: window.location.href,
                            captchaElementsCount: captchaElements.length
                        };
                    }''')

                    logging.info(f"WeChat content check: {content_check}")

                    # 如果检测到验证码，直接认定为失败
                    if content_check['hasCaptcha']:
                        logging.error("WeChat verification/captcha detected, marking as failed")
                        return False

                    if not content_check['hasContent']:
                        logging.warning("WeChat article content not properly loaded, may be blocked")
                        # 如果内容长度太短，也可能是验证码页面
                        if content_check['pageTextLength'] < 500:
                            logging.error("Page content too short, likely a verification page")
                            return False

                except asyncio.TimeoutError:
                    logging.warning("WeChat fixes timeout, continuing anyway")
            
            # 生成PDF的参数
            pdf_options = {
                'path': pdf_filename,
                'format': 'A4',
                'printBackground': True,
                'displayHeaderFooter': False,
                'margin': {
                    'top': '20mm',
                    'right': '10mm',
                    'bottom': '20mm',
                    'left': '10mm'
                }
            }
            
            logging.info(f"Converting to PDF: {pdf_filename}")
            try:
                # 添加PDF生成超时控制
                await asyncio.wait_for(page.pdf(pdf_options), timeout=60.0)

                # 对于微信文章，生成PDF后再次检查内容
                if 'mp.weixin.qq.com' in url:
                    final_check = await page.evaluate('''() => {
                        const pageText = document.body.textContent || document.body.innerText || '';
                        const captchaKeywords = [
                            '验证码', '验证', 'captcha', 'verification',
                            '请输入验证码', '安全验证', '人机验证',
                            'wappoc_appmsgcaptcha', '微信安全验证',
                            '为了确认本次访问为正常用户行为'
                        ];

                        const hasCaptcha = captchaKeywords.some(keyword =>
                            pageText.toLowerCase().includes(keyword.toLowerCase())
                        );

                        return {
                            hasCaptcha: hasCaptcha,
                            textLength: pageText.length,
                            hasRealContent: pageText.length > 500 && !hasCaptcha
                        };
                    }''')

                    if final_check['hasCaptcha'] or not final_check['hasRealContent']:
                        logging.error(f"WeChat PDF contains verification content, deleting and marking as failed")
                        # 删除生成的无效PDF
                        if os.path.exists(pdf_filename):
                            os.remove(pdf_filename)
                        return False

                logging.info(f"Successfully saved {pdf_filename}")
            except asyncio.TimeoutError:
                logging.error(f"PDF generation timeout for {pdf_filename}")
                return False
        
        # 下载页面中的PDF附件
        await download_pdf_attachments(page, url, output_dir, title)
        return True
        
    except Exception as e:
        logging.error(f"Pyppeteer error converting {url} to PDF: {e}")
        return False
    finally:
        if browser:
            try:
                # 添加浏览器关闭超时控制
                await asyncio.wait_for(browser.close(), timeout=10.0)
                logging.info("Browser closed successfully")
            except asyncio.TimeoutError:
                logging.warning("Browser close timeout, forcing termination")
                # 强制终止浏览器进程
                try:
                    if hasattr(browser, 'process') and browser.process:
                        browser.process.terminate()
                        await asyncio.sleep(1)
                        if browser.process.poll() is None:
                            browser.process.kill()
                except Exception as e:
                    logging.error(f"Error force closing browser: {e}")
            except Exception as e:
                logging.error(f"Error closing browser: {e}")

async def auto_scroll(page):
    """自动滚动页面以确保所有内容加载"""
    await page.evaluate('''async () => {
        window.scrollTo(0, 0);
        return new Promise((resolve) => {
            let totalHeight = 0;
            const distance = 500;
            const scrollDelay = 500;
            
            const timer = setInterval(() => {
                const scrollHeight = document.body.scrollHeight;
                window.scrollBy(0, distance);
                totalHeight += distance;
                
                if (totalHeight >= scrollHeight) {
                    clearInterval(timer);
                    setTimeout(resolve, scrollDelay);
                }
            }, scrollDelay);
        });
    }''')
    await asyncio.sleep(1)  # 额外等待1秒
async def remove_unnecessary_elements(page):
        """精准移除百家号侧边栏及相关元素"""
        await page.evaluate('''() => {
            // 核心选择器 - 百家号特化组件
            const baijiaSpecific = [
                // 作者信息模块
                ".author-info-wrapper", ".author-info-inner", ".user-info", 
                ".author-title", ".author-verify-info", ".mod-author",
                // 关注按钮区域
                ".follow-button-wrapper", ".focus-btn-wrapper", 
                // 作者相关区域
                ".author-selection", ".article-selection", ".vertical-card",
                ".latest-container", ".latest-article-list",
                // 推荐区域
                ".index-module_articleWrap", ".related-content", 
                ".recommend-container", ".recommend-box", ".recommend-right",
                // 热搜模块
                ".hot-list", ".hot-news-wrapper", ".hot-content",
                ".hot-search-container", ".hot-search-box", 
                // 侧边工具及悬浮元素
                ".side-toolbar", ".tool-bar", ".side-anchor",
                ".side-recommend", ".right-is-recommend",
                ".floating-header", ".fixed-footer-bar",
                // 功能选项区域
                ".option-wrapper", ".option-list", ".ecom-icon",
                ".custom-side", ".xgplayer_controller"
            ];
            
            // 通用冗余元素
            const genericSelectors = [
                'header', 'footer', 'nav', 'aside', 'form',
                '.header', '.footer', '.navbar', '.ad-header',
                '.top-bar', '.recommend-header', '.rec-title',
                '.ad-container', '.ad', '.banner', '.popup', '.modal',
                '.overlay', '.cookie-banner', '.comments',  
                'div[data-ad]', 'div[data-role="ad"]', 
                'script', 'iframe', 'ins', 'object', 'video',
                '.float-panel', '.qrcode-box', '.signature',
                '.fixed-track',
                // 隐藏的播放器控件
                '.button-wrapper', '.pan-btn', '.right-icon-text',
                '.more-button', '.more-btn', '.comment-wrap',
                '.topic-box'
            ];

            // 最终选择器列表（合并特化和通用）
            const combinedSelectors = [
                ...baijiaSpecific, 
                ...genericSelectors
            ];

            // 批量移除匹配元素
            combinedSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    try {
                        el.parentNode?.removeChild(el);
                    } catch (e) {
                        console.warn('移除元素失败:', e);
                    }
                });
            });

            // 特殊处理：百家号侧边卡片容器
            const sideCards = document.querySelector('.slide-banner');
            if (sideCards) {
                try {
                    sideCards.parentNode.removeChild(sideCards);
                } catch (e) {
                    console.warn('移除侧边卡片失败');
                }
            }

            // 处理固定定位元素(二次清理)
            document.querySelectorAll('*').forEach(el => {
                const style = getComputedStyle(el);
                if (['fixed', 'sticky'].includes(style.position)) {
                    try {
                        el.style.display = 'none';
                    } catch (e) {
                        console.warn('隐藏固定元素失败');
                    }
                }
            });

            
        }''')





async def fix_wechat_images(page):
    """修复微信公众号图片加载问题"""
    await page.evaluate('''() => {
        // 移除图片加载覆盖层
        const overlays = document.querySelectorAll('.rich_media_loading, .img_loading, .placeholder');
        overlays.forEach(el => el.remove());

        // 处理懒加载图片
        const lazyImages = document.querySelectorAll('img[data-src], img[data-custom], img[data-ratio]');
        lazyImages.forEach(img => {
            if (img.dataset.src) {
                img.src = img.dataset.src;
            } else if (img.dataset.custom && img.dataset.custom.includes('imgurl=')) {
                img.src = decodeURIComponent(img.dataset.custom.split('imgurl=')[1].split('&')[0]);
            }
        });

        // 显示隐藏的图片
        const hiddenImages = document.querySelectorAll('img[style*="display:none"], img[style*="visibility:hidden"]');
        hiddenImages.forEach(img => {
            img.style.display = 'inline-block';
            img.style.visibility = 'visible';
            img.style.maxWidth = '100%';
        });

        // 移除不必要的微信元素
        const elementsToRemove = [
            '.rich_media_tool', '.rich_media_meta_list', '.rich_media_extra',
            '.qr_code_pc', '.reward_qrcode', '.share_notice', '.function_mod',
            '#js_pc_qr_code', '.rich_media_global_msg', '.rich_media_area_primary',
            '.rich_media_area_extra', '.rich_media_inner', '.rich_media_area_primary'
        ];

        elementsToRemove.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                try {
                    el.remove();
                } catch (e) {
                    console.warn('Failed to remove element:', e);
                }
            });
        });

        // 确保内容区域可见
        const content = document.querySelector('#js_content, .rich_media_content');
        if (content) {
            content.style.display = 'block';
            content.style.visibility = 'visible';
            content.style.width = '100%';
        }

        // 移除图片覆盖后的交互元素
        const imageBlocks = document.querySelectorAll('.img-wrap, .photo-frame, .image-container');
        imageBlocks.forEach(container => {
            // 保留内部图片，移除容器装饰
            const img = container.querySelector('img');
            if (img) {
                container.parentNode.replaceChild(img, container);
            }
        });

        // 强制显示所有文本内容
        const textElements = document.querySelectorAll('p, div, span, h1, h2, h3, h4, h5, h6');
        textElements.forEach(el => {
            if (el.style.display === 'none') {
                el.style.display = 'block';
            }
            if (el.style.visibility === 'hidden') {
                el.style.visibility = 'visible';
            }
        });
    }''')
    # 等待图片和内容加载
    await asyncio.sleep(3)

async def main():
    output_directory = "./downloaded_pdfs"
    input_links_file = "./extracted_data_ver2.txt"
    
    if not os.path.exists(output_directory):
        os.makedirs(output_directory)

    try:
        with open(input_links_file, 'r', encoding='utf-8') as f:
            links_data = f.readlines()
    except FileNotFoundError:
        logging.error(f"Input file not found: {input_links_file}")
        return

    failed_urls = []
    for i, line in enumerate(links_data):
        parts = line.strip().split(',', 1)
        if len(parts) == 2:
            title, url = parts
            data_type = None
            if not title or not url:
                logging.warning(f"Skipping invalid line: {line.strip()}")
                continue

            # 不再跳过任何URL，都尝试下载，失败的会自动记录到failed_urls

            logging.info(f"Processing {i+1}/{len(links_data)}: {title}")
            success = await download_and_convert_to_pdf_pyppeteer(title, url, output_directory)
            if not success:
                failed_urls.append((title, url, data_type))
            time.sleep(2)  # 请求间隔
        else:
            logging.warning(f"Skipping malformed line: {line.strip()}")
    
    if failed_urls:
        logging.info("\n--- Failed URLs ---")
        with open(os.path.join(output_directory, 'failed_downloads.txt'), 'w', encoding='utf-8') as f:
            for title, url, data_type in failed_urls:
                f.write(f"{title},{url},{data_type}\n")
                logging.info(f"Title: {title}, URL: {url}, Type: {data_type}")
        logging.info("Failed URLs saved to failed_downloads.txt")
    else:
        logging.info("All pages processed successfully.")

if __name__ == '__main__':
    asyncio.run(main())
