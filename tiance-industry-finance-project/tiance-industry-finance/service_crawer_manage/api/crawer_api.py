﻿#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@Time    :   2025/07/15 14:40:55
<AUTHOR>   W<PERSON><PERSON><PERSON> HONG 
@Email    :   <EMAIL>
@File    :   crawer.py
@Project    :   tiance-industry-finance
'''

from configs.api_config import ApiConfig
from fastapi import APIRouter
from service_data_manage.api.routes import data_manage_route

api_router = APIRouter()

api_router.include_router(data_manage_route.router, prefix=ApiConfig.DATA_MANAGE_ROUTE, tags=["数据同步"])
#!/usr/bin/env python
# -*- encoding: utf-8 -*-


from configs.api_config import ApiConfig
from fastapi import APIRouter
from service_crawer_manage.api.router import crawer_router

api_router = APIRouter()

api_router.include_router(crawer_router.router, prefix="/api/webpage", tags=["爬虫管理"])

# def get_db(request: Request):
#     db = request.app.state.SessionLocal()
#     try:
#         yield db
#     finally:
#         db.close()
