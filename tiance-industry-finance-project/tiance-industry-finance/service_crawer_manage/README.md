# 网页管理服务文档

## 概述

网页管理服务提供了完整的网页下载、管理、重试和入库功能，支持批量操作和后台任务处理。

## 目录结构

```
service_crawer_manage/
├── __init__.py
├── api/
│   ├── __init__.py
│   └── webpage_api.py          # API接口定义
├── entity/
│   ├── __init__.py
│   └── webpage_entity.py       # 数据库实体类
├── service/
│   ├── __init__.py
│   ├── webpage_download_service.py    # 下载服务
│   ├── webpage_query_service.py       # 查询服务
│   ├── webpage_file_service.py        # 文件服务
│   ├── webpage_retry_service.py       # 重试服务
│   └── webpage_storage_service.py     # 入库服务
├── ddl/
│   └── webpage_manage_ddl.sql  # 数据库DDL脚本
├── test_webpage_service.py     # 测试脚本
└── README.md                   # 本文档
```

## 数据库设计

### 1. 网页下载任务表 (webpage_download_job)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| job_id | VARCHAR(64) | 任务ID（主键） |
| job_name | VARCHAR(255) | 任务名称 |
| job_status | VARCHAR(20) | 任务状态 |
| total_count | INT | 总网页数量 |
| success_count | INT | 成功下载数量 |
| failed_count | INT | 失败下载数量 |
| create_time | DATETIME | 创建时间 |
| start_time | DATETIME | 开始时间 |
| end_time | DATETIME | 结束时间 |
| update_time | DATETIME | 更新时间 |

### 2. 网页下载详情表 (webpage_download_detail)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 自增ID |
| job_id | VARCHAR(64) | 任务ID |
| webpage_url | VARCHAR(2048) | 网页地址 |
| webpage_name | VARCHAR(500) | 保存网页的名字 |
| data_type | VARCHAR(100) | 数据类型 |
| download_status | VARCHAR(20) | 下载状态 |
| file_path | VARCHAR(1024) | 文件保存地址 |
| file_size | BIGINT | 文件大小（字节） |
| error_message | TEXT | 错误信息 |
| retry_count | INT | 重试次数 |
| create_time | DATETIME | 创建时间 |
| download_time | DATETIME | 下载完成时间 |
| update_time | DATETIME | 更新时间 |

## API接口

### 1. 创建网页下载任务
- **接口**: `POST /api/webpage/download/create`
- **功能**: 创建网页下载任务，支持单次或批量请求
- **入参**: 网页地址、数据类型、保存网页的名字（数组形式）
- **出参**: job_id

### 2. 查询任务状态
- **接口**: `GET /api/webpage/job/{job_id}/status`
- **功能**: 查询任务执行状态和统计信息

### 3. 查询任务详情
- **接口**: `GET /api/webpage/job/{job_id}/details`
- **功能**: 查询任务的详细下载记录（分页）

### 4. 查询任务列表
- **接口**: `GET /api/webpage/jobs`
- **功能**: 查询所有任务列表（分页）

### 5. 下载文件（精确匹配）
- **接口**: `GET /api/webpage/download/file`
- **功能**: 通过job_id + 网页地址 + 保存网页名字下载文件

### 6. 下载文件（按URL）
- **接口**: `GET /api/webpage/download/file/by-url`
- **功能**: 通过网页地址下载文件（返回最新的成功下载）

### 7. 查询文件列表
- **接口**: `GET /api/webpage/job/{job_id}/files`
- **功能**: 通过job_id查询文件列表信息

### 8. 重新下载
- **接口**: `POST /api/webpage/retry`
- **功能**: 重新下载失败的网页，支持多种方式

### 9. 文件入库
- **接口**: `POST /api/webpage/storage`
- **功能**: 文件入库，支持批量入库和指定文件入库

## 使用说明

### 1. 数据库初始化

首先执行DDL脚本创建数据库表：

```sql
-- 执行 ddl/webpage_manage_ddl.sql 中的脚本
```

### 2. 创建下载任务

```python
from service_crawer_manage.service.webpage_download_service import WebpageDownloadService

# 准备网页请求数据
webpage_requests = [
    {
        'url': 'https://example.com/page1',
        'data_type': '资讯',
        'name': '示例页面1'
    },
    {
        'url': 'https://example.com/page2',
        'data_type': '政策',
        'name': '示例页面2'
    }
]

# 创建下载任务
result = WebpageDownloadService.create_download_job(
    webpage_requests=webpage_requests,
    job_name="示例下载任务"
)

print(f"任务创建成功，Job ID: {result['job_id']}")
```

### 3. 查询任务状态

```python
from service_crawer_manage.service.webpage_query_service import WebpageQueryService

# 查询任务状态
status = WebpageQueryService.query_job_status(job_id)
print(f"任务状态: {status['job_status']}")
print(f"成功数量: {status['success_count']}")
print(f"失败数量: {status['failed_count']}")
```

### 4. 重试失败的下载

```python
from service_crawer_manage.service.webpage_retry_service import WebpageRetryService

# 重试指定任务的失败下载
result = WebpageRetryService.retry_failed_downloads(job_id=job_id)
print(f"重试任务启动: {result['message']}")
```

### 5. 文件入库

```python
from service_crawer_manage.service.webpage_storage_service import WebpageStorageService

# 批量入库指定任务的文件
result = WebpageStorageService.store_files_by_job_id(job_id)
print(f"入库任务启动: {result['message']}")
```

## 注意事项

1. **环境要求**: 确保conda环境为`tiance-industry-finance`
2. **依赖关系**: 依赖现有的`service_crawer_manage/script/download_pdfs.py`、`service_crawer_manage/script/retry_failed_downloads.py`和`service_crawer_manage/script/upload_files_and_infos.py`
3. **文件存储**: 下载的文件默认保存在`./temp_webpage_downloads`目录
4. **后台任务**: 下载、重试和入库都是后台异步执行
5. **会话管理**: 已修复SQLAlchemy会话绑定问题，支持跨线程操作
6. **向后兼容**: 保持原有脚本文件的功能不变
7. **文件名编码**: 已修复中文文件名下载时的编码问题，支持UTF-8编码
8. **文件名安全化**: 自动处理文件名中的特殊字符，确保文件系统兼容性

## 问题修复

### 文件下载编码问题修复

**问题**: 下载包含中文字符的文件时出现编码错误：
```
'latin-1' codec can't encode characters in position 29-31: ordinal not in range(256)
```

**解决方案**:
1. **URL编码**: 使用`urllib.parse.quote`对文件名进行URL编码
2. **文件名安全化**: 添加`_sanitize_filename`方法处理特殊字符
3. **HTTP头部优化**: 使用`filename*=UTF-8''`格式支持Unicode文件名

**修复后的功能**:
- ✅ 支持中文文件名下载
- ✅ 自动处理特殊字符（如`<>:"/\|?*`）
- ✅ 防止文件名过长（限制255字符）
- ✅ 处理空文件名情况

### 多线程和数据库连接问题修复

**问题1**: SQLAlchemy连接池错误：
```
pymysql.err.InterfaceError: (0, '')
```

**问题2**: Pyppeteer多线程错误：
```
signal only works in main thread of the main interpreter
```

**问题3**: 重新下载后任务状态未更新

**解决方案**:
1. **进程隔离**: 使用`subprocess`在独立进程中执行下载，避免多线程问题
2. **连接管理**: 改进数据库连接的异常处理和重连机制
3. **状态同步**: 添加重试完成后的任务状态更新逻辑
4. **脚本分离**: 创建独立的下载和重试脚本，避免线程冲突

**修复后的功能**:
- ✅ 解决Pyppeteer多线程信号问题
- ✅ 修复SQLAlchemy连接池异常
- ✅ 重试后自动更新任务状态
- ✅ 改进数据库连接异常处理
- ✅ 进程级别的下载隔离

### 状态自动检查和更新功能

**新增功能**: 每次查询时自动检查并更新任务和详情状态

**实现机制**:
1. **WebpageStatusManager**: 新增状态管理器类，负责状态检查逻辑
2. **自动状态检查**: 在所有查询方法中集成状态检查
3. **智能状态更新**: 根据文件存在性和时间自动更新状态
4. **数据一致性**: 确保查询结果反映最新的实际状态

**状态检查规则**:
- ✅ **文件丢失检查**: SUCCESS状态的记录如果文件不存在，自动标记为FAILED
- ✅ **下载超时检查**: DOWNLOADING状态超过30分钟自动标记为FAILED
- ✅ **任务状态同步**: 根据详情状态自动更新任务的整体状态
- ✅ **统计信息更新**: 自动重新计算成功/失败数量

**集成的查询方法**:
- `query_job_status()` - 查询任务状态时自动检查
- `query_job_details()` - 查询任务详情时自动检查
- `query_jobs_list()` - 查询任务列表时自动检查
- `download_file_by_job_and_name()` - 文件下载时自动检查
- `download_file_by_url()` - 按URL下载时自动检查
- `download_file_by_job_id()` - 文件列表查询时自动检查

**使用示例**:
```python
# 查询任务状态 - 自动检查并更新状态
status = WebpageQueryService.query_job_status(job_id)
# 返回的状态已经是检查更新后的最新状态

# 查询任务列表 - 自动检查所有任务状态
jobs = WebpageQueryService.query_jobs_list(page=1, page_size=20)
# 所有任务状态都已自动检查更新
```

## 状态说明

### 任务状态 (job_status)
- `CREATED`: 已创建
- `RUNNING`: 运行中
- `COMPLETED`: 已完成
- `FAILED`: 失败

### 下载状态 (download_status)
- `PENDING`: 未下载
- `DOWNLOADING`: 正在下载
- `SUCCESS`: 下载成功
- `FAILED`: 下载失败
- `STORED`: 已入库
