#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 20:00:00
# <AUTHOR> Assistant
# @File         : test_fixes.py
# @Description  : 测试修复的功能
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from service_crawer_manage.service.webpage_download_service import WebpageDownloadService
from service_crawer_manage.service.webpage_query_service import WebpageQueryService
from service_crawer_manage.service.webpage_retry_service import WebpageRetryService

def test_download_and_retry():
    """测试下载和重试功能"""
    try:
        print("开始测试下载和重试功能...")
        
        # 1. 创建下载任务
        webpage_requests = [
            {
                'url': 'https://www.example.com/test1',
                'data_type': '资讯',
                'name': '测试网页1'
            },
            {
                'url': 'https://www.example.com/test2',
                'data_type': '政策',
                'name': '测试网页2'
            }
        ]
        
        print("创建下载任务...")
        result = WebpageDownloadService.create_download_job(
            webpage_requests=webpage_requests,
            job_name="测试任务_修复验证"
        )
        
        job_id = result['job_id']
        print(f"任务创建成功: {job_id}")
        
        # 2. 等待一段时间让下载任务执行
        print("等待下载任务执行...")
        time.sleep(10)
        
        # 3. 查询任务状态
        print("查询任务状态...")
        status = WebpageQueryService.query_job_status(job_id)
        print(f"任务状态: {status['job_status']}")
        print(f"成功数量: {status['success_count']}")
        print(f"失败数量: {status['failed_count']}")
        
        # 4. 如果有失败的，测试重试功能
        if status['failed_count'] > 0:
            print("测试重试功能...")
            retry_result = WebpageRetryService.retry_failed_downloads(job_id=job_id)
            print(f"重试任务启动: {retry_result['message']}")
            
            # 等待重试完成
            time.sleep(10)
            
            # 再次查询状态
            print("重试后查询任务状态...")
            status_after_retry = WebpageQueryService.query_job_status(job_id)
            print(f"重试后任务状态: {status_after_retry['job_status']}")
            print(f"重试后成功数量: {status_after_retry['success_count']}")
            print(f"重试后失败数量: {status_after_retry['failed_count']}")
        
        print("测试完成！")
        return job_id
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        return None

def test_database_connection():
    """测试数据库连接管理"""
    try:
        print("测试数据库连接管理...")
        
        # 多次查询测试连接稳定性
        for i in range(5):
            print(f"第{i+1}次查询...")
            jobs = WebpageQueryService.query_jobs_list(page=1, page_size=5)
            print(f"查询到 {len(jobs.get('data', []))} 个任务")
            time.sleep(1)
        
        print("数据库连接测试完成！")
        
    except Exception as e:
        print(f"数据库连接测试失败: {str(e)}")

if __name__ == "__main__":
    print("开始修复功能测试...")
    print("=" * 50)
    
    # 测试数据库连接
    test_database_connection()
    
    print("\n" + "=" * 50)
    
    # 测试下载和重试
    job_id = test_download_and_retry()
    
    if job_id:
        print(f"\n测试成功完成！任务ID: {job_id}")
    else:
        print("\n测试失败！")
