#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :request_entity.py
@Description  :
<AUTHOR>
@Date         :2024/11/15 17:32:21
'''
from typing import Dict

from pydantic import BaseModel, Field

from configs.collection_config import CollectionConfig
from configs.prompt_config import PromptConfig
from configs.run_config import RunConfig
from entity.request_example import CHAIN_STRUCTURE, NODE_COMPANIES


class ExtensionRequest(BaseModel):
    node_name_info: str = Field(..., example="专用车产业链|橡胶", description="产业链节点信息名称")
    stream_type: str = Field(..., example="上游", description="上下游类型")


class SearchDocumentRequest(BaseModel):
    doc_keyword: str = Field(..., example="工业机器人", description="文档关键词")


class Acronym2FullName(BaseModel):
    acronym: str = Field(..., example="华润微", description="文档关键词")


class ResearchExtensionRequest(BaseModel):
    node_name_info: str = Field(..., example="新能源汽车产业链", description="产业链节点信息名称")
    stream_type: str = Field(..., example="up_stream", description="上下游类型")
    document_list: list = Field(..., example=["5e74cb834eb2922500b391697a990c4c"], description="产业链节点信息名称")


class AIAssistantRequest(BaseModel):
    user_id: str = Field(..., example="user", description="用户唯一标识")
    question: str = Field(..., example="你好", description="用户问题")


class ResearchReportStructureRequest(BaseModel):
    model: str = Field(..., examples=["qwen2.5-72B"], description="模型名")
    k: int = Field(..., examples=[5], description="检索内容数")
    collection_name: str = Field(default=CollectionConfig.RESEARCH_REPORT_LABEL_INFO,
                                 examples=[CollectionConfig.RESEARCH_REPORT_LABEL_INFO],
                                 description="文档所在mongo集合")
    mongodb_id: str = Field(..., examples=["b6abd927a86d40b891f10ade0fd130c0"], description="研报mongo_id")
    industry: str = Field(..., examples=["工业机器人"], description="产业链名称")
    system_prompt: str = Field(..., examples=["你是一个专业的产业链抽取助手"], description="系统提示词")


class ResearchReportComposeRequest(BaseModel):
    model: str = Field(default="DeepSeek-R1-671B", examples=["qwen2.5-72B"], description="模型名")
    k: int = Field(default=5, examples=[5], description="检索内容数")
    mongodb_ids: list[str] = Field(..., examples=[["b6abd927a86d40b891f10ade0fd130c0"]], description="研报mongo_id列表")
    industry: str = Field(..., examples=["工业机器人"], description="产业链名称")
    system_prompt: str = Field(default=PromptConfig.RESEACH_REPORT_COMPOSE_SYSTEM_PROMPT,
                               examples=[PromptConfig.RESEACH_REPORT_COMPOSE_SYSTEM_PROMPT], description="系统提示词")


class SearchKeyCompaniesRequest(BaseModel):
    model: str = Field(..., examples=["qwen2.5-72B"], description="模型名")
    k: int = Field(..., examples=[5], description="检索内容数")
    collection_name: str = Field(default=CollectionConfig.RESEARCH_REPORT_LABEL_INFO,
                                 examples=[CollectionConfig.RESEARCH_REPORT_LABEL_INFO],
                                 description="文档所在mongo集合")
    mongodb_id: str = Field(..., examples=["b6abd927a86d40b891f10ade0fd130c0"], description="文档mongo_id")
    industry: str = Field(..., examples=["工业机器人"], description="产业链名称")
    system_prompt: str = Field(default=PromptConfig.SEARCH_KEY_COMPNIES_SYSTEM_PROMPT,
                               examples=[PromptConfig.SEARCH_KEY_COMPNIES_SYSTEM_PROMPT], description="系统提示词")
    link: str = Field(..., examples=["核心零部件"], description="环节名称")


class SearchKeyCompaniesComposeRequest(BaseModel):
    model: str = Field(..., examples=["qwen2.5-72B"], description="模型名")
    k: int = Field(..., examples=[5], description="检索内容数")
    collection_names: list[str] = Field(default=[],
                                        examples=[[CollectionConfig.RESEARCH_REPORT_LABEL_INFO] * 5],
                                        description="文档所在mongo集合，与以下mongodb_ids列表一一对应。"
                                                    "若为空，则会在对应api中自动生成 [CollectionConfig.RESEARCH_REPORT_LABEL_INFO] * len(mongodb_ids) ，"
                                                    "即len(mongodb_ids)个研报的集合名组成的列表")
    mongodb_ids: list[str] = Field(..., examples=[
        ["b6abd927a86d40b891f10ade0fd130c0", "fa1c26252598433b8ddbc157f03edbdb", "bc141372c98e433db2ff273e56c8dde7",
         "8f1d1a1089464a55898931f1d862938c", "5cd87797d8fb41f78bb40285ee6ddf18"], ], description="研报mongo_id")
    industry: str = Field(..., examples=["工业机器人"], description="产业链名称")
    # chain_structure为None时自动根据mongodb_ids中的研报生成产业链结构
    chain_structure: dict | None = Field(..., examples=[CHAIN_STRUCTURE], description="产业链结构")
    epochs: int = Field(default=1, examples=[1], description="单环节调用次数")


class GetFullnameCompaniesRequest(BaseModel):
    industry: str = Field(..., examples=["工业机器人"], description="产业链名称")
    node_companies: dict = Field(..., examples=[NODE_COMPANIES], description="企业名称")


class GetInudstryChainRequest(BaseModel):
    model: str = Field(..., examples=["qwen2.5-72B"], description="模型名")
    mongodb_ids: list[str] = Field(..., examples=[
        ["b6abd927a86d40b891f10ade0fd130c0", "fa1c26252598433b8ddbc157f03edbdb", "bc141372c98e433db2ff273e56c8dde7",
         "8f1d1a1089464a55898931f1d862938c", "5cd87797d8fb41f78bb40285ee6ddf18"]], description="研报mongo_id列表")
    industry: str = Field(..., examples=["工业机器人"], description="产业链名称")


class SearchRelatedCompaniesRequest(BaseModel):
    key_companies: list[dict] = Field(..., examples=[[{'abb': '抚顺特钢', 'name': '抚顺特殊钢股份有限公司',
                                                       'is_listed': '是', 'stream_type': '上游',
                                                       'industry_position': '', 'update_time': '2025-03-03'},
                                                      {'abb': '伊之密', 'name': '伊之密股份有限公司', 'is_listed': '是',
                                                       'stream_type': '下游', 'industry_position': '',
                                                       'update_time': '2025-03-03'}]], description="中心客群")


class ResearchReportExtensionReuquest(BaseModel):
    mongodb_ids: list[str] = Field(..., examples=[
        ["b6abd927a86d40b891f10ade0fd130c0", "fa1c26252598433b8ddbc157f03edbdb", "bc141372c98e433db2ff273e56c8dde7",
         "8f1d1a1089464a55898931f1d862938c", "5cd87797d8fb41f78bb40285ee6ddf18"]], description="研报mongo_id列表")
    industry: str = Field(..., examples=["工业机器人"], description="产业链名称")


class IndustryChainMapRequest(BaseModel):
    node_name_info: str = Field(..., example="工业机器人", description="产业链名称")
    stream_type: str = Field(..., example="上游", description="上下游类型")


class GetKeyCompaniesRequest(BaseModel):
    industry: str = Field(..., examples=["工业机器人"], description="产业链名称")


class RelatedCompaniesRequest(BaseModel):
    industry: str = Field(..., examples=["工业机器人"], description="产业链名称")


class KeyCompaniesRequest(BaseModel):
    industry: str = Field(..., examples=["工业机器人"], description="产业链名称")


class IndustryEvaluationRequest(BaseModel):
    industry: str = Field(..., examples=["工业机器人"], description="产业链名称")
    prompt: str = Field(..., examples=["请对工业机器人产业链进行评价与总结"], description="系统提示词")


class FinancialSuggestionsRequest(BaseModel):
    industry: str = Field(..., examples=["工业机器人"], description="产业链名称")
    prompt: str = Field(..., examples=[
        "你是一名专业的融资营销分析专家。请从产业链金融风控评估的角度，对工业机器人产业链提出优质的建议。"],
                        description="系统提示词")
    suggestion_type: str = Field(..., examples=["产业链金融风控评估"], description="建议类型")


class NodeAnalysisRequest(BaseModel):
    node_name_info: str = Field(..., example="工业机器人", description="产业链节点信息名称")
    stream_type: str = Field(..., example="上游", description="上下游类型")


class CompanySummaryRequest(BaseModel):
    industry: str = Field(..., examples=["工业机器人"], description="产业链名称")
    company_type: str = Field(..., example="中心客群", description="企业类型")
    prompt: str = Field(..., example=PromptConfig.COMPANY_SUMMARY_SYSTEM_PROMPT, description="大模型提示词")


class LabelExtractRequest(BaseModel):
    model: str = Field(RunConfig.MAX_LLM_MODEL_NAME, examples=[RunConfig.MAX_LLM_MODEL_NAME], description="模型名")
    k: int = Field(5, examples=[5], description="检索内容数")
    doc_id_and_type: list[Dict[str, str]] = \
        Field(...,
              examples=[[{"doc_type": "research_report", "mongodb_id": "b6abd927a86d40b891f10ade0fd130c0"},
                         {"doc_type": "research_report", "mongodb_id": "fa1c26252598433b8ddbc157f03edbdb"}]],
              description=("文档的mongoid及其对应的文档类型。"
                           "其中 'doc_type' 取值为research_report/invoice/customs/credit_report中的任意一种，分别对应研报/发票/海关/授信报告"
                           " 'mongodb_id' 表示文档对应的 mongodb id ")),
    system_prompt: str = Field(..., examples=[
        """你是一个专业的$["工业机器人","新能源汽车","新材料","低空经济']$产业链抽取助手..."""],
                               description="系统提示词")
    is_cached: bool = Field(True, examples=[True], description="是否启用缓存")


class LabelMergeRequest(BaseModel):
    mongodb_id: str = Field(..., examples=["338ea781843e45bd892593587724f63a"], description="标签提取返回的id")
    model: str = Field(default="qwen2.5-72B", examples=["qwen2.5-72B"], description="模型名")
    prompt: str = Field(default="", examples=[PromptConfig.LABEL_COMPOSE_SYSTEM_PROMPT], description="检索内容数")
    merge_type: str = Field(default="", examples=["Frequency"], description="合并类型")
    is_ai_extend: bool = Field(default=False, examples=[False], description="产业链名称")
    is_cached: bool = Field(default=True, examples=[True], description="是否使用缓存")


class CompanyFullNameSupplementRequest(BaseModel):
    node_companies: list = Field(...)
    model: str = Field(...)


class GetCompnayName(BaseModel):
    mongodb_id: str = Field(..., examples=["5818626bdbd84976a224f65f84e6a3e5"], description="文档对应的mongodb_id")
    mongodb_collection_name: str = Field(..., examples=["news_label_info"], description="文档存放集合名")
