#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :response_entity.py
@Description  :
<AUTHOR>
@Date         :2024/11/13 16:42:50
'''

from pydantic import BaseModel, Field

class SuccessResponse(BaseModel):
    code: int = Field(200, example=200, description="响应码")
    message: str = Field("success", example="success", description="响应信息")
    data: dict = Field(..., example={}, description="响应数据")

class FalseResponse(BaseModel):
    code: int = Field(500, example=500, description="响应码")
    message: str = Field("false", example="success", description="响应信息")
    data: dict = Field(..., example={"error": "detail"}, description="响应数据")
