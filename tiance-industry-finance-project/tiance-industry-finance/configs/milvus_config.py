#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time    : 2024/10/21 15:20
# <AUTHOR> <PERSON><PERSON><PERSON>
# @Email   : <EMAIL>
# @File    : milvus_config.py
# @Project : tiance-industry-finance
"""
class MilvusConfig(object):
    """
    milvus配置
    """

    # 知识库连接信息
    MILVUS_CONNECT_INFO = {
        "uri": "http://{}".format("10.8.21.165:19530"),
        "user": "",
        "password": "",
        "secure": False,
        "db_name": "Industry_finance",
    }

    # Milvus默认输出fields
    DEFAULT_MILVUS_OUTPUT_FIELDS = ["chunk_index","mongodb_id","mongodb_collection",
                                    "file_source","file_url","file_title","file_type","file_parse_time","file_flag",
                                    "chunk_content_father","chunk_content_son","chunk_type","chunk_vector","file_name", "file_time","chunk_content"]
    # 向量数据库中文档解析后文档内容所在列
    MILVUS_DOC_CONTENT_FIELD_NAME = "chunk_content_father"


