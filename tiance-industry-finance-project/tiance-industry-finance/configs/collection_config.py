#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :collection_config.py
@Description  :
<AUTHOR>
@Date         :2024/11/14 15:54:26
'''


class CollectionConfig(object):
    """
    集合配置
    """
    # 公告集合名称
    # NOTICE = "source_notice_bak"
    NOTICE = "notice_label_info"

    # 公告集合名称
    NOTICE_ALL = "notice_label_info"

    # 研报集合名称
    RESEARCH = "source_research"

    # 知识库集合名称
    KNOWLEDGE = "industry_finance_collection_test"

    # 通用扩展产业链集合名称
    COMMON_EXTENSION = "general_industry_information"

    # 节点分析集合名称
    NODE_ANALYSIS = "node_analysis"

    # 产品扩展集合名称
    PRODUCT_EXTENSION = "product_extension_information"

    # 研报扩展集合名称
    RESEARCH_EXTENSION = "research_extension_information"

    # 问答助手集合名称
    AI_ASSISTANT = "ai_assistant"

    INDUSTRY_COMPANY_EXTENSION = "industry_company_extension"

    # 产业研报文档集合名称
    RESEARCH_REPORT_INFO = "research_report_info"

    # 产业研报Label集合名称
    RESEARCH_REPORT_LABEL_INFO = "research_report_label_info_new"

    # 资讯新闻Label集合名称
    NEWS_LABEL_INFO = "news_label_info_new"

    # 发票文档Label集合名称
    INVOICE_LABEL_INFO = "invoice_label_info_new"

    # 海关文档Label集合名称
    CUSTOMS_LABEL_INFO = "customs_label_info_new"

    # 授信报告文档Label集合名称
    CREDIT_REPORT_LABEL_INFO = "credit_report_label_info"

    # 从发票、海关、授信报告文档抽取出公司名称及其对应产品
    EXTRACTED_COMPANY_AND_RELATED_PRODUCT = "extracted_company_and_related_product"

    # 上市企业信息集合名称
    LISTED_COMPANIES_INFO = "listed_companies_cn"

    # 企业基础信息集合名称
    BASIC_INFO = "listed_companies_basic"

    # 品牌信息集合名称
    BRAND_INFO = "listed_companies_ppxx"

    # 招股说明书信息集合名称
    IPO_INFO = "company_relation_history"

    # 年报信息表
    ANNUAL_REPORT_INFO = "annual_report_history"

    # 产业链结构集合
    CHAIN_STRUCTURE = "chain_structure"

    # 中心客群集合
    KEY_COMPANIES = "key_companies"

    # 客群总结集合
    COMPANY_SUMMARY = "company_summary"

    # 融资建议集合
    FINANCHIAL_SUGGESTIONS = "financial_suggestions"

    # 产业评价集合
    INDUSTRY_EVALUATION = "industry_evaluation"

    # 产业权威名录集合
    INDUSTRY_POSITION_INFO = "industry_position_companies"

    # 产业链关联产品&商品表
    INDUSTRY_CHAIN_RELATED_PRODUCTS = "industry_chain_related_products"

    # 标签提取
    LABEL_EXTRACT = "label_extract_history"

    # 标签提取展示
    LABEL_EXTRACT_PERFORM = "label_extract_perform_history"

    # 标签融合
    LABEL_MERGE = "label_merge_history"

    # 标签展示
    LABEL_MERGE_PERFORM = "label_merge_perform_history"

    # 所有年报向量库
    KNOWLEDGE_REPORT_ALL = "research_report_vector_library"

    # research_report_structure 接口缓存
    CACHE_RESEARCH_REPORT_STRUCTURE = "cache_research_report_structure"

    # search_key_companies 接口缓存
    CACHE_SEARCH_KEY_COMPANIES = "cache_search_key_companies"

    # get_fullname_companies 接口缓存
    CACHE_GET_FULLNAME_COMPANIES = "cache_get_fullname_companies"

    # 研报抽取历史记录
    ANNUAL_REPORT_HISTORY = "annual_report_history"

    # 研报抽取历史记录
    COMPANY_RELATION_HISTORY = "company_relation_history"

    # 年报抽取向量库
    # ANNUAL_REPORT_MILVUS = "industry_finance_annual_all"
    # ANNUAL_REPORT_MILVUS = "industry_finance_annual_other"
    ANNUAL_REPORT_MILVUS = "notice_vector_library"
    # ANNUAL_REPORT_MILVUS = "industry_finance_new_energy_annual_report"
    # ANNUAL_REPORT_MILVUS = "industry_finance_low_altitude_economy_annual_report"
    # ANNUAL_REPORT_MILVUS = "industry_finance_new_material_annual_report"

    # 招股说明书抽取向量库

    # PROSPECTUS_REPORT_MILVUS = "industry_finance_new_energy_prospectus"
    # PROSPECTUS_REPORT_MILVUS = "industry_finance_new_material_prospectus"
    # PROSPECTUS_REPORT_MILVUS = "industry_finance_prospectus_other"
    # PROSPECTUS_REPORT_MILVUS = "industry_finance_prospectus_all"
    PROSPECTUS_REPORT_MILVUS = "notice_vector_library"

    POLICY_LABEL_INFO = "policy_label_info_new"

    NEWS_LABEL_INFO = "news_label_info_new"
