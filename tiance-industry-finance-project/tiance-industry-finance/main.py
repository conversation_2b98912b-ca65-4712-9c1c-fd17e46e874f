#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File         :main.py
@Description  :
<AUTHOR>
@Date         :2025/02/27 16:16:42
"""

from utils.sql_util import SQLUtil
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from configs.api_config import ApiConfig

# 导入数据管理模块
from service_data_manage.api import data_manage
# 导入数据管理模块
from service_crawer_manage.api import crawer_api

from utils.mongodb_util import MongodbUtil
from utils.minio_util import MinIoUtil
from utils.log_util import LogUtil


# 初始化日志
LogUtil.init(process_name="module_industry_chain_extension")
# 初始化数据库连接
MongodbUtil.connect()
MinIoUtil.connect()
SQLUtil.connect()

# 初始化API
app = FastAPI(
    title="产业链扩展接口服务",
    description="提供产业链扩展服务"
)

# 配置跨源资源共享（CORS）中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,  # 允许凭据
    allow_methods=["*"],  # 允许所有 HTTP 方法
    allow_headers=["*"],  # 允许所有请求头
)

# 注册数据管理模块路由
app.include_router(data_manage.api_router, prefix=ApiConfig.ROOT_ROUTE)
# 注册爬取网页路由
app.include_router(crawer_api.api_router, prefix=ApiConfig.ROOT_ROUTE)



if __name__ == "__main__":

    uvicorn.run(app, host=ApiConfig.SERVICE_IP, port=ApiConfig.SERVICE_PORT)
