#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :llm_model.py
@Description  :
<AUTHOR>
@Date         :2024/11/15 17:04:31
'''

import openai
from configs.model_config import ModelConfig

class LlmModel(object):
    """
    大语言模型
    """
    @staticmethod
    def get_model_client(model):
        """
        获取模型客户端
        :return:
        """
        # 返回模型客户端
        return openai.AsyncClient(api_key=ModelConfig.LLM_API_KEY, base_url=ModelConfig.LLM_MODEL_CHOICES.get(model))
