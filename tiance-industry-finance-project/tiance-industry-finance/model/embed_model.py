#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :embed_model.py
@Description  :
<AUTHOR>
@Date         :2024/11/15 15:22:06
'''

import openai
from configs.model_config import ModelConfig

class EmbedModel(object):
    """
    向量模型
    """
    @staticmethod
    def get_model_client():
        """
        获取模型客户端
        :return:
        """
        # 返回模型客户端
        return openai.AsyncClient(api_key=ModelConfig.EMBED_API_KEY, base_url=ModelConfig.EMBED_API_BASE)
