#!/usr/bin/env python
# -*- encoding: utf-8 -*-


from configs.api_config import ApiConfig
from fastapi import APIRouter
from service_data_manage.api.routes import data_manage_route

api_router = APIRouter()

api_router.include_router(data_manage_route.router, prefix=ApiConfig.DATA_MANAGE_ROUTE, tags=["数据同步"])


# def get_db(request: Request):
#     db = request.app.state.SessionLocal()
#     try:
#         yield db
#     finally:
#         db.close()
