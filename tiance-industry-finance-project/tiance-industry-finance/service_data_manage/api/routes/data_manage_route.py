#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 12:30:00
# <AUTHOR> Assistant
# @File         : data_manage_route.py
# @Description  : 数据管理API路由
"""
import io
import re
import os
import aiofiles
import asyncio
import pandas as pd
from fastapi import APIRouter, UploadFile, Body, File, Form
from fastapi.responses import StreamingResponse
from fastapi import APIRouter, HTTPException, Body, Query, Response
from typing import Union, List, Optional
from fastapi import APIRouter, Depends, HTTPException
from utils.mongodb_util import MongodbUtil

from pydantic import Field

from service_data_manage.entity.data_manage_entity import (
    CompanyDataStatsRequest,
    CompanyMainRequest,
    CompanyMainResponse,
    CompanyUpdateRequest,
    CompanyBatchUpdateRequest,
    CompanyDataDownloadRequest,
    CompanyBatchUpdateRequest,
    CompanyAddRequest,
    CompanyBatchAddRequest,
    CompanyDelRequest,
    CompanyBatchDelRequest,
    DelDocumentRequest,
    UpdateDocumentRequest,
    QueryDocumentRequest,
    FileDataStatsRequest
)
from entity.response_entity import SuccessResponse, FalseResponse
from service_data_manage.service.data_manage_service import (
    CompanyDataStatsService,
    CompanySearchService,
    CompanyUpdateService,
    CompanyDataUploadService,
    CompanyDataDownloadService,
    CompanyUpdateService,
    CompanyAddService,
    CompanyDelService,
    DocumentUpdateService,
    DocumentDelService,
    DuplicateCompanyError,
    QueryDocumentService,
    FileDataStatsService
)
from service_data_manage.service.data_manage_service import DocumentParserService
from utils.log_util import LogUtil
from utils.ret_util import RetUtil
from utils.sql_util import SQLUtil
from utils.minio_util import MinIoUtil
from utils.mongodb_util import MongodbUtil
from service_data_manage.entity.data_manage_entity import DocumentParseDictEntity

router = APIRouter()


@router.post("/company_data_stats", summary="公司数据总量查询")
async def company_data_stats(request: CompanyDataStatsRequest):
    """
    公司数据总量查询接口

    功能说明：
    1. 查询公司数据总量（status=1的记录总数）
    2. 查询上市公司总数（StockAbbr不为空的记录总数）
    3. 查询当日更新总数（update_time大于当日0点的记录总数）

    Args:
        request: 查询请求参数（当前无需参数）

    Returns:
        SuccessResponse: 包含统计数据的响应
        - total_count: 公司数据总量
        - listed_count: 上市公司总数
        - today_updated_count: 当日更新总数
        - query_date: 查询日期
    """
    try:
        # 记录请求日志
        LogUtil.log_json(describe="公司数据总量查询请求", kwargs=dict(request))

        # 调用服务层获取统计数据
        stats_data = CompanyDataStatsService.get_company_data_statistics()

        # 记录返回日志
        LogUtil.log_json(describe="公司数据总量查询返回结果", kwargs=stats_data)

        return RetUtil.response_ok(data=stats_data)

    except Exception as e:
        error_detail = f"公司数据总量查询失败：{str(e)}"
        LogUtil.error(msg=error_detail)

        error_data = {
            "error": error_detail,
            "error_type": "company_data_stats_error"
        }

        return RetUtil.response_error(data=error_data)


@router.post("/company_update", summary="公司数据修改（支持单个和批量）")
async def company_update(
        request: Union[CompanyUpdateRequest, CompanyBatchUpdateRequest, List[CompanyUpdateRequest]]):
    """
    公司数据修改接口（支持单个和批量）

    功能说明：
    1. 支持单个公司修改和批量公司修改
    2. 批量修改时，先检查所有数据，任何一个不符合就返回错误
    3. ChiName（中文名称）为必填项，不能为空
    4. ChiName修改后自动将原有值补充到PreName中
    5. PreName中的数据以逗号分割，自动去重
    6. 自动更新update_time
    7. 修改ChiName时会进行查重验证

    Args:
        request: 公司修改请求参数
        - 单个修改：CompanyUpdateRequest 对象
        - 批量修改：CompanyBatchUpdateRequest 对象或 List[CompanyUpdateRequest]

    Returns:
        SuccessResponse: 包含修改结果的响应
        - 单个修改：返回单个公司的修改结果
        - 批量修改：返回批量修改结果或第一个错误信息
    """
    try:
        # 判断是单个修改还是批量修改
        if isinstance(request, list):
            # 直接传入的列表
            companies_data = [
                {
                    "company_code": company.company_code,
                    "chi_name": company.chi_name,
                    "chi_name_abbr": company.chi_name_abbr,
                    "eng_name": company.eng_name
                }
                for company in request
            ]
            is_batch = True
            LogUtil.log_json(describe="公司数据批量修改请求（列表）", kwargs={"total_count": len(request)})
        elif hasattr(request, 'companies'):
            # CompanyBatchUpdateRequest 对象
            companies_data = [
                {
                    "company_code": company.company_code,
                    "chi_name": company.chi_name,
                    "chi_name_abbr": company.chi_name_abbr,
                    "eng_name": company.eng_name
                }
                for company in request.companies
            ]
            is_batch = True
            LogUtil.log_json(describe="公司数据批量修改请求", kwargs={"total_count": len(request.companies)})
        else:
            # 单个修改
            is_batch = False
            LogUtil.log_json(describe="公司数据修改请求", kwargs=dict(request))

        if is_batch:
            # 批量修改
            result = CompanyUpdateService.batch_update_companies(companies_data)
        else:
            # 单个修改
            result = CompanyUpdateService.update_company_info(
                company_code=request.company_code,
                chi_name=request.chi_name,
                chi_name_abbr=request.chi_name_abbr,
                eng_name=request.eng_name
            )

        # 记录返回日志
        LogUtil.log_json(describe="公司数据修改返回结果", kwargs=result)

        return RetUtil.response_ok(data=result)

    except ValueError as e:
        # 参数验证错误
        error_detail = f"参数验证失败：{str(e)}"
        LogUtil.error(msg=error_detail)

        error_data = {
            "error": error_detail,
            "error_type": "validation_error"
        }

        # 添加相关信息
        if hasattr(request, 'company_code'):
            error_data["company_code"] = request.company_code
        elif hasattr(request, 'companies'):
            error_data["total_count"] = len(request.companies)
        elif isinstance(request, list):
            error_data["total_count"] = len(request)

        return RetUtil.response_error(data=error_data)

    except Exception as e:
        # 其他错误
        error_detail = f"公司数据修改失败：{str(e)}"
        LogUtil.error(msg=error_detail)

        error_data = {
            "error": error_detail,
            "error_type": "company_update_error"
        }

        # 添加相关信息
        if hasattr(request, 'company_code'):
            error_data["company_code"] = request.company_code
        elif hasattr(request, 'companies'):
            error_data["total_count"] = len(request.companies)
        elif isinstance(request, list):
            error_data["total_count"] = len(request)

        return RetUtil.response_error(data=error_data)


@router.post("/company_data_upload", summary="从Excel表格导入新增公司数据到数据库")
async def company_data_upload(excel_file: UploadFile) -> Response:
    """
    公司数据导入接口 -- 导入字段均和数据库保持一致。
    导入要求CreditCode、ChiName必填。数据库其余字段选填。
    status（1）、creat_time（新增当日时间） 和update_time（新增当日时间）为默认，不暴露在接口入参中，
    新增数据需要对CreditCode、ChiName进行查重。

    :param excel_file: excel文件的字节流
    :return:
    """
    try:
        content = await excel_file.read()
        df = pd.read_excel(io.BytesIO(content))

        # 数据验证
        CompanyDataUploadService.validate(df)

        # 数据导入
        CompanyDataUploadService.upload(df)

        return RetUtil.response_ok(data={})
    except Exception as e:
        return RetUtil.response_error(message=f"数据导入失败：{str(e)}")


@router.post("/company_data_download", response_model=None, summary="根据公司编号列表，导出Excel文件")
async def company_data_download(request: CompanyDataDownloadRequest) -> StreamingResponse | Response:
    """
    根据公司编号列表，导出Excel文件
    :param request:
    :return: 文件数据流
    """

    try:
        file_stream = CompanyDataDownloadService.download(request.company_code_list)

        return StreamingResponse(file_stream,
                                 media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                 headers={"Content-Disposition": "attachment", "filename": "company_data.xlsx"})

    except Exception as e:
        return RetUtil.response_error(message=f"数据导出失败：{str(e)}")


@router.post("/company_add", summary="公司数据新增")
async def company_add(request: Union[CompanyAddRequest, CompanyBatchAddRequest, List[CompanyAddRequest]] = Body(...)) -> Response:
    """
       公司数据新增接口

       功能说明：
       1. 支持三种输入形式：
           - 单个公司对象：AddCompanyMain
           - 批量添加（带字段名）：AddCompanyRequest（包含 company_list 字段）
           - 批量添加（裸列表）：List[AddCompanyMain]
       2. 会校验公司名称和统一社会信用代码是否重复
       3. 自动生成 CompanyCode，默认 status=1，并设置创建时间与更新时间

       Args:
           request: 公司新增请求体

       Returns:
           dict: 包含新增结果（成功数、失败数、详细信息）
       """

    try:
        # 请求体结构判断
        if isinstance(request, list):
            company_data_list = [CompanyAddService.convert_to_dict(c.dict()) for c in request]
            LogUtil.log_json(describe="公司数据批量新增请求（裸数组）", kwargs={"total_count": len(request)})

        elif hasattr(request, "company_list"):
            company_data_list = [CompanyAddService.convert_to_dict(c.dict()) for c in request.company_list]
            LogUtil.log_json(describe="公司数据批量新增请求（字段封装）",
                             kwargs={"total_count": len(request.company_list)})

        else:
            # 单个公司数据
            company_data_list = [CompanyAddService.convert_to_dict(request.dict())]
            LogUtil.log_json(describe="公司数据单个新增请求", kwargs=request.dict())

        add_results = CompanyAddService.add_companies(company_data_list)

        # 记录返回日志
        LogUtil.log_json(describe="公司数据新增返回结果", kwargs=add_results)

        return RetUtil.response_ok(data=add_results)

    except DuplicateCompanyError as e:
        # 处理数据重复异常
        LogUtil.warn(msg=f"公司数据新增失败: {e.detail}")
        error_data = {"error": e.detail, "error_type": "duplicate_company_error"}
        return RetUtil.response_error(data=error_data)

    except Exception as e:
        # 处理其他异常
        error_detail = f"公司数据新增失败：{str(e)}"
        LogUtil.error(msg=error_detail)
        error_data = {"error": error_detail, "error_type": "add_company_error"}
        return RetUtil.response_error(data=error_data)


@router.post("/company_del", summary="公司数据删除")
async def company_del(request: Union[CompanyDelRequest, CompanyBatchDelRequest, List[CompanyDelRequest]]) -> Response:
    """
    公司数据删除接口

    功能说明：
    1. 支持三种输入形式：
        - 单个公司对象：CompanyDelRequest
        - 批量删除（带字段名）：CompanyBatchDelRequest（包含 company_list 字段）
        - 批量删除（裸列表）：List[CompanyDelRequest]
    2. 根据CompanyCode进行逻辑删除（将 status 设置为 0）
    3. 若公司不存在或已删除，返回失败信息

    Args:
        request: 公司删除请求体

    Returns:
        dict: 包含删除结果（总数、成功数、失败数、详细信息）
    """

    try:
        # 类型判断
        if isinstance(request, list):
            company_list = request
            LogUtil.log_json(describe="公司删除请求（裸列表）", kwargs={"total": len(request)})
        elif hasattr(request, "company_list"):
            company_list = request.company_list
            LogUtil.log_json(describe="公司删除请求（封装对象）", kwargs={"total": len(request.company_list)})
        else:
            company_list = [request]
            LogUtil.log_json(describe="公司删除请求（单个对象）", kwargs={"CreditCode": request.CreditCode})

        # 调用服务层执行删除操作
        delete_results = CompanyDelService.delete_companies(company_list)

        # 记录返回日志
        LogUtil.log_json(describe="公司数据删除返回结果", kwargs=delete_results)

        return RetUtil.response_ok(data=delete_results)

    except HTTPException as http_ex:
        error_data = {
            "error": http_ex.detail,
            "error_type": "http_error"
        }
        LogUtil.error(msg=http_ex.detail)
        return RetUtil.response_error(data=error_data)

    except Exception as e:
        error_detail = f"公司数据删除失败：{str(e)}"
        LogUtil.error(msg=error_detail)

        error_data = {
            "error": error_detail,
            "error_type": "company_delete_error"
        }

        return RetUtil.response_error(data=error_data)
    
@router.post("/company_search", summary="公司查询", response_model=List[CompanyMainResponse])
async def search_company(request: CompanyMainRequest) -> Response:
    """
    公司数据查询接口

    功能说明：
    1. 根据公司名称或进行模糊查询
    2. 根据公司统一社会信用代码进行精确查询
    3. 查询全部公司
    2. 支持分页查询
    

    Args:
        request: 公司查询请求体
        page_num 分页页码
        page_size 分页每页条数
        company_name_info 公司名称或统一社会信用代码
        company_name_info 输入公司名称 做模糊查询
        company_name_info 输入统一社会信用代码 做精确查询
        company_name_info 为空 查询全部公司
        

    Returns:
        JSONresponse: {"code":200, "status": True, "message": "success", "data": data}
    """
    try:
        db = SQLUtil.get_session()
        if request.company_name_info == "":
            result,count = CompanySearchService.query_all_company(db=db, page_num=request.page_num,
                                                            page_size=request.page_size)
        else:

            ## 判断传入的是否是creditcode
            pattern = r'^[A-Z0-9]+$'
            if bool(re.fullmatch(pattern, request.company_name_info)):
                result,count = CompanySearchService.query_company_by_creditcode(db=db, creditcode=request.company_name_info, page_num=request.page_num, page_size=request.page_size)
                # creditcode查询
            else:
                # 公司名称查询
                result,count = CompanySearchService.query_company_by_name(db=db, company_name=request.company_name_info, page_num=request.page_num, page_size=request.page_size)
        data = SQLUtil.models_to_list(result)
        process_data = {
            "result": data,
            "total": count
        }
        return RetUtil.response_ok(data=process_data)

    except Exception as e:
        detail = f"服务器错误:{e}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        return RetUtil.response_error(data=detail)

@router.post("/batch_upload_file", summary="批量上传文件")
async def batch_upload_file(
        dataset_type: str = Form(..., example="研报",
                                 description="数据类型:【研报】【公告】【发票】【海关】【政策】【资讯】"),
        files: List[UploadFile] = File(..., description="可上传多个文件")
) -> Response:
    """
        批量上传【文件】接口

        功能说明：
           - 批量上传文件，将文件原始数据存入minio,文件相关信息存入mongodb

        Args：
           - dataset_type: 数据类型（str的【研报】【公告】【发票】【海关】【政策】【资讯】）
           - files: 文件列表，列表中元素为文件（UploadFile）

        Returns：
           - JSONresponse: {"code":200, "status": True, "message": "success", "data": data}
    """
    try:
        # 1. 入参数据验证
        # 数据类型
        if dataset_type not in ["研报","公告","发票","海关","政策","资讯"]:
            error_msg = "dataset_type输入错误，只允许【研报】【公告】【发票】【海关】【政策】【资讯】"
            LogUtil.error(msg=error_msg)
            return RetUtil.response_error(message=error_msg)
        # 缓存文件夹
        os.makedirs(DocumentParseDictEntity.FILE_CACHE_FOLDER, exist_ok=True)
        # 检查重复文件 直接返回错误
        collection_name = DocumentParseDictEntity.MONGO_DATASET_TYPE_DICT.get(dataset_type)
        for file in files:
            result = MongodbUtil.query_docs_by_condition(collection_name,
                                                         search_condition={"file.file_title": file.filename})
            result_doc = list(result)
            if len(result_doc) >= 1:
                error_msg = f"当前库中存在重名文件：{file.filename}"
                LogUtil.error(msg=error_msg)
                return RetUtil.response_error(message=error_msg)
        # 返回入参数据
        file_name_list = [file.filename for file in files]
        input_data = {"file_name": file_name_list, "dataset_type": dataset_type}
        LogUtil.log_json(describe="批量上传文件入参数据", kwargs=input_data)

        # 2.并行处理文件
        tasks = []
        for file_obj in files:
            tasks.append(DocumentParserService.process_single_file(file_obj, dataset_type))
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 3.结果后处理
        result_data = []
        all_success = True
        error_messages = []
        for idx, result in enumerate(results):
            file_obj = files[idx]
            if isinstance(result, Exception):  # 失败，记录错误信息
                error_data = {"file_name": file_obj.filename, "status": "error", "error": str(result)}
                result_data.append(error_data)
                error_messages.append(f"文件 '{file_obj.filename}' 处理失败: {str(result)}")
                all_success = False
            else:  # 成功，添加成功结果
                result_data.append({"file_name": file_obj.filename, "status": "success", "data": result})

        if error_messages:
            LogUtil.error(msg="\n".join(error_messages))

        # 响应处理
        if all_success:
            LogUtil.log_json(describe="批量上传文件返回数据", kwargs=result_data)
            return RetUtil.response_ok(data=result_data)
        else:
            LogUtil.error(msg=f"批量处理失败: {str(result_data)}")
            return RetUtil.response_error(message="部分文件处理失败", data=result_data)

    except Exception as e:
        LogUtil.error(msg=f"批量处理失败: {str(e)}")
        return RetUtil.response_error(message=f"批量处理失败: {str(e)}")


@router.post("/batch_upload_subfile", summary="批量上传附件")
async def batch_upload_subfile(
        dataset_type: str = Form(..., description="数据类型:【研报】【公告】【发票】【海关】【政策】【资讯】"),
        mongodb_id: str = Form(..., description="需要存入的mongodb的collection的id"),
        files: List[UploadFile] = File(..., description="可上传多个文件")
) -> Response:
    """
        批量上传【文件附件】接口

        功能说明：
           - 批量上传附件文件，将文件附件存入minio,文件相关信息存入指定mongodb_id的collection下

        Args:
           - dataset_type: 数据类型（str的【研报】【公告】【发票】【海关】【政策】【资讯】）
           - mongodb_id: 需要存入的mongodb的collection的id
           - files: 文件列表，列表中元素为文件（UploadFile）

        Returns：
           - JSONresponse: {"code":200, "status": True, "message": "success", "data": data}
    """

    try:
        # 1. 入参数据验证
        # 验证数据类型参数
        if dataset_type not in ["研报", "公告", "发票", "海关", "政策", "资讯"]:
            error_msg = "dataset_type输入错误，只允许【研报】【公告】【发票】【海关】【政策】【资讯】"
            LogUtil.error(msg=error_msg)
            return RetUtil.response_error(message=error_msg)

        # 确保缓存文件夹存在
        os.makedirs(DocumentParseDictEntity.FILE_CACHE_FOLDER, exist_ok=True)

        # 2. 检查主文档是否存在
        collection_name = DocumentParseDictEntity.MONGO_DATASET_TYPE_DICT.get(dataset_type)
        existing_docs = list(MongodbUtil.query_docs_by_condition(collection_name, {"_id": mongodb_id}))
        result_doc = list(existing_docs)
        if len(result_doc) == 0:
            error_msg = "未找到主文件信息，请检查数据类型及id"
            LogUtil.error(msg=error_msg)
            return RetUtil.response_error(message=error_msg)

        # 检查重复文件 直接抛异常
        old_file_name_list = []
        for file_minio_path in result_doc[0].get("minio").get("minio_document_path"):
            fiel_name = file_minio_path.split("/")[-1]
            old_file_name_list.append(fiel_name)

        for file in files:
            if file.filename in old_file_name_list:
                error_msg = f"当前collection存在重名文件：{file.filename}"
                LogUtil.error(msg=error_msg)
                return RetUtil.response_error(message=error_msg)

        # 记录请求
        file_name_list = [file.filename for file in files]
        input_data = {"file_name": file_name_list, "dataset_type": dataset_type, "mongodb_id": mongodb_id}
        LogUtil.log_json(describe="上传附件入参数据", kwargs=input_data)

        # 3. 处理每个附件
        minio_paths = []
        for file_obj in files:
            try:
                # 上传单个附件
                minio_path = await DocumentParserService.process_single_subfile(file_obj, dataset_type)
                minio_paths.append(minio_path)
            except Exception as e:
                error_msg = f"文件 '{file_obj.filename}' 处理失败: {str(e)}"
                LogUtil.error(msg=error_msg)
                return RetUtil.response_error(message=error_msg)

        # 4. 更新主文档的minio路径
        old_minio_list = existing_docs[0].get("minio", {}).get("minio_document_path", [])
        new_minio_list = old_minio_list + minio_paths

        # 更新
        update_operation = {"$set": {"minio.minio_document_path": new_minio_list}}
        MongodbUtil.update_one(collection_name, {"_id": mongodb_id}, update_operation)

        # 5. 获取更新后的文档并返回
        updated_docs = list(MongodbUtil.query_docs_by_condition(collection_name, {"_id": mongodb_id}))
        if updated_docs:
            LogUtil.log_json(describe="上传附件返回数据", kwargs=updated_docs[0])
            return RetUtil.response_ok(data=updated_docs[0])
        else:
            error_msg = "更新后文档未找到"
            LogUtil.error(msg=error_msg)
            return RetUtil.response_error(message=error_msg)

    except Exception as e:
        LogUtil.error(msg=f"上传附件处理失败: {str(e)}")
        return RetUtil.response_error(message=f"上传附件处理失败: {str(e)}")


@router.post("/update_document", summary="更新文档数据", description="更新指定文档的字段信息")
async def update_document(
        request: UpdateDocumentRequest
):
    """
    更新指定文档的字段信息

    本接口用于修改MongoDB中已存在的文档内容，支持原子性更新单个或多个字段。
    只更新请求中包含的字段，不影响未包含的原有字段。

    Args:
        request (UpdateDocumentRequest): 更新请求参数对象，包含:
            - collection_type (str): 集合类型/名称（枚举值，如'article','user'等）
            - document_id (str): 要更新的文档唯一标识符
            - update_fields (dict): 需要更新的字段键值对集合，格式如:
                {
                    "file_title": "新标题",
                    "file_type": "文件格式",
                    "file_url": "文件地址"
                    "file_flag": {file_flag字典}
                }

    Returns:
        dict: 返回操作结果，结构为:
            {
                "message": "更新成功",       # 操作结果消息
                "document_id": "xxx",      # 被更新的文档ID
                "updated_fields": ["field1", "field2"]  # 实际被更新的字段列表
            }

    """
    try:
        # 记录请求日志
        request_log_description = "更新文档数据请求"
        request_log_kwargs = {"collection_type": request.collection_type, "document_id": request.document_id,
                              "update_fields": request.dict()}
        LogUtil.log_json(describe=request_log_description, kwargs=request_log_kwargs)

        # 调用服务层更新文档
        update_result = DocumentUpdateService.update_document(request.collection_type, request.document_id,
                                                              request.dict())

        # 记录返回日志
        response_log_description = "更新文档数据返回结果"
        response_log_kwargs = {"update_result": update_result}
        LogUtil.log_json(describe=response_log_description, kwargs=response_log_kwargs)

        return RetUtil.response_ok(data=update_result)

    except Exception as e:
        error_detail = f"更新文档数据失败：{str(e)}"
        LogUtil.error(msg=error_detail)

        return RetUtil.response_error(message=error_detail)


@router.post("/soft_delete_document", summary="软删除文档", description="将文档状态标记为已删除（status=0）")
async def soft_delete_document(
        request: DelDocumentRequest
):
    """
    软删除指定文档（将状态设为0）

    该操作不会物理删除数据，只是将文档标记为删除状态，便于后续恢复或审计。
    被软删除的文档将不会出现在常规查询结果中。

    Args:
        request (DelDocumentRequest): 删除请求参数对象，包含:
            - **collection_type** (str): 文档数据类型/集合名称
            - **document_id** (str): 要删除的文档唯一标识符

    Returns:
        dict: 返回操作结果，结构为:
            {
                "message": "删除成功",  # 操作结果消息
                "document_id": "xxx", # 被删除的文档ID
                "data_type": "xxx"     # 文档所属的数据类型
            }
    """
    try:
        # 记录请求日志
        request_log_description = "软删除文档请求"
        request_log_kwargs = {"data_type": request.collection_type, "document_id": request.document_id}
        LogUtil.log_json(describe=request_log_description, kwargs=request_log_kwargs)

        # 调用服务层进行软删除操作
        del_result = DocumentDelService.soft_delete_document(request.collection_type, request.document_id)

        # 记录返回日志
        response_log_description = "软删除文档返回结果"
        response_log_kwargs = {"del_result": del_result}
        LogUtil.log_json(describe=response_log_description, kwargs=response_log_kwargs)

        return RetUtil.response_ok(data=del_result)

    except Exception as e:
        error_detail = f"软删除文档失败：{str(e)}"
        LogUtil.error(msg=error_detail)
        return RetUtil.response_error(message=error_detail)


@router.post("/document_query", summary="文档模糊查询服务")
async def document_query(request: QueryDocumentRequest):
    """
    文档模糊查询接口

    功能说明：
    1. 根据数据集类型和文件标题进行模糊查询
    2. 返回匹配文档的分页结果（每页10条,最多1000条)
    3. 仅返回状态为有效的文档（status=1）

    参数说明：
    - dataset_type: 数据集类型，可选值：资讯/政策/公告/海关/发票/研报
    - file_title: 文件标题关键词（支持模糊匹配）
    - page: 请求的页码（默认为1）

    返回字段说明：
    - code: 响应状态码（200表示成功）
    - message: 响应消息
    - data: 响应数据，包含以下字段：
      - total: 匹配的文档总数
      - result: 查询结果列表，包含以下字段：
        * _id: 文档唯一标识符（MongoDB ObjectId转换的字符串）
        * data_type: 数据类型（如新闻、政策等）
        * data_source: 数据来源（如网站、机构等）
        * file_title: 文件标题
        * file_type: 文件类型（如PDF、HTML等）
        * file_url: 文件原始URL
        * file_flag: 文件标志字典，包含：
        * release_time: 文件发布时间（ISO格式）
        * crawling_time: 爬取时间（ISO格式）
        * parse_time: 解析时间（ISO格式）
        * minio_name: MinIO存储桶名称
        * minio_document_path: MinIO中文件存储路径
        * milvus_db_name: Milvus数据库名称
        * milvus_collection_name: Milvus集合名称
        * status: 文档状态（1-有效，0-无效）
    """
    try:
        # 记录请求日志
        LogUtil.log_json(describe="文档查询请求", kwargs=request.dict())

        # 调用查询服务
        query_result = QueryDocumentService.query_documents(
            collection_type=request.collection_type,
            file_title=request.file_title,
            page=request.page
        )

        # 检查查询结果是否包含错误
        if "error" in query_result:
            error_detail = f"文档查询失败: {query_result.get('error')}"
            LogUtil.error(msg=error_detail)
            return FalseResponse(
                message=error_detail,
                error_type="document_query_error",
                data=query_result
            )

        # 记录成功响应日志
        LogUtil.log_json(describe="文档查询成功", kwargs=query_result)

        return RetUtil.response_ok(data=query_result)

    except Exception as e:
        # 处理未捕获的异常
        error_detail = f"文档查询服务异常: {str(e)}"
        LogUtil.error(msg=error_detail)
        error_data = {
            "error": error_detail,
            "error_type": "document_query_error"
        }

        return RetUtil.response_error(data=error_data)

@router.post("/query_collection_type", summary="获取集合类型", description="获取集合类型")
async def update_document():

    try:
        collection_type =["研报","公告","发票","海关","政策","资讯"]

        return RetUtil.response_ok(data=collection_type)

    except Exception as e:
        data = '服务器错误'
        return RetUtil.response_error(data=data)

@router.post("/file_data_stats", summary="文件数据总量查询")
async def file_data_stats(request: FileDataStatsRequest):
    try:
        result = FileDataStatsService.file_data_stats(request.collection_type)

        return RetUtil.response_ok(data=result)

    except Exception as e:
        data = f'服务器错误{e}'
        return RetUtil.response_error(data=data)
