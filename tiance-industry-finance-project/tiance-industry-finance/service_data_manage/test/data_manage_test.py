#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time    : 2025/6/30 10:57
# <AUTHOR> hejunjie
# @Email   : <EMAIL>
# @File    : data_manage_test.py
# @Project : tiance-industry-finance
"""
import asyncio
import json

from fastapi import UploadFile

from service_data_manage.api.routes.data_manage_route import company_data_upload, company_data_download
from service_data_manage.entity.data_manage_entity import CompanyDataDownloadRequest
from utils.log_util import LogUtil


def Test_company_data_upload():
    upload_excel_file_path = 'D:\\Code\\PyCharm Projects\\tiance-industry-finance\\test_scripts\\data\\导入导出测试数据\\CompanyMain_upload_test.xlsx'

    with open(upload_excel_file_path, "rb") as f:
        res = asyncio.run(company_data_upload(
            excel_file=UploadFile(file=f)
        ))
        LogUtil.info(res.body.decode())


def Test_company_data_download():
    res = asyncio.run(company_data_download(
        CompanyDataDownloadRequest(
            company_code_list=["C000017637", "C000017638", "C000017639"]
        )
    ))

    async def save_stream_file():
        file_name = res.headers.raw[1][1].decode()
        with open(file_name, "wb") as f:
            async for chunk in res.body_iterator:
                f.write(chunk)

    asyncio.run(save_stream_file())

    LogUtil.info(str(res))

    """
    curl http://127.0.0.1:9029/tc/llm/base/data/manage/company_data_download -X POST -H "Content-Type: application/json" -d "{\"company_code_list\":[\"C000017637\", \"C000017638\", \"C000017639\"]}" --output ./company_data.xlsx
    """


if __name__ == '__main__':
    LogUtil.init("Test data_manage_service")

    # Test_company_data_upload()

    Test_company_data_download()
