#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time    : 2024/10/21 15:26
# <AUTHOR> <PERSON><PERSON><PERSON>
# @Email   : <EMAIL>
# @File    : milvus_util.py
# @Project : tiance-industry-finance
"""

from typing import Optional
from pymilvus.milvus_client import IndexParams
from pymilvus import MilvusClient, CollectionSchema
from configs.milvus_config import MilvusConfig
from typing import List, Dict


class MilvusUtil(object):
    """
    Milvus工具类
    """

    def __init__(self):
        """
        连接数据库
        :return:
        """
        # 获取连接配置
        connect_config = MilvusConfig.MILVUS_CONNECT_INFO
        if connect_config is None:
            raise ValueError(f"向量数据库的连接配置未找到")
        # 获得连接对象
        self.milvus_client = MilvusClient(**connect_config)

    def create_collection(
        self, collection_name: str, kb_schema: CollectionSchema, kb_index: IndexParams
    ):
        """
        创建知识库
        :param collection_name: 知识库名称
        :param kb_schema: 知识库结构
        :param kb_index: 知识库索引
        :return:
        """
        # 创建知识库
        self.milvus_client.create_collection(
            collection_name=collection_name, schema=kb_schema, index_params=kb_index
        )

    def drop_collection(self, collection_name: str):
        """
        删除知识库
        :param collection_name: 知识库名称
        :return:
        """
        # 删除知识库
        self.milvus_client.drop_collection(collection_name)

    def collection_is_exists(self, collection_name: str):
        """
        知识库是否存在
        :param collection_name: 知识库名称
        :return:
        """
        # 知识库是否存在
        return self.milvus_client.has_collection(collection_name)

    def add_document(self, collection_name: str, data: list):
        """
        添加文档
        :param collection_name: 知识库名称
        :param data: 需要入库的数据
        :return:
        """
        self.milvus_client.insert(collection_name=collection_name, data=data)

    def del_document(self, collection_name: str, del_conditions: str):
        """
        删除文档
        :param collection_name: 知识库名称
        :param del_conditions: 删除条件
        :return:
        """
        self.milvus_client.delete(
            collection_name=collection_name, filter=del_conditions
        )

    def query_by_scalar(
        self, collection_name: str, query_conditions: str, **other_conditions: dict
    ):
        """
        根据标量进行查询
        :param collection_name: 知识库名称
        :param query_conditions: 查询条件
        :param other_conditions: 其它条件，如output_fields或limit等
        :return:
        """
        # 根据标量查询文档
        docs = self.milvus_client.query(
            collection_name=collection_name, filter=query_conditions, **other_conditions
        )
        return docs

    def document_is_exists(self, collection_name: str, query_conditions: str):
        """
        文档是否已经存在
        :param collection_name: 知识库名称
        :param query_conditions: 查询条件
        :return:
        """
        # 限制条件
        other_conditions = {"output_fields": ["pk"], "limit": 1}
        # 查询文档
        docs = self.query_by_scalar(
            collection_name=collection_name,
            query_conditions=query_conditions,
            **other_conditions,
        )
        if docs is None or docs == []:
            return False
        return True

    def search_by_vector(
        self,
        collection_name: str,
        vector: List[List[float]],
        limit: int,
        expr: Optional[str] = None,
        search_params: Optional[dict] = None,
        **other_params: Dict,
    ):
        """
        根据向量进行查询
        :param collection_name: 知识库名称
        :param vector: 向量
        :param limit: 条数限制
        :param search_params: 查询参数
        :param other_params: 其它参数，如output_fields
        :return:
        """
        # 根据向量查询文档
        docs = self.milvus_client.search(
            collection_name=collection_name,
            data=vector,
            limit=limit,
            filter=expr,
            output_fields=MilvusConfig.DEFAULT_MILVUS_OUTPUT_FIELDS,
            search_params=search_params,
            **other_params,
        )
        return docs
    
    def search_by_filter(
        self,
        collection_name: str,
        filter: str = "",
        output_fields=MilvusConfig.DEFAULT_MILVUS_OUTPUT_FIELDS,
    ):
        """
        根据filter进行查询
        :param collection_name: 知识库名称
        filter: 过滤条件
        :return:
        """

        docs = self.milvus_client.query(
                collection_name= collection_name,
                filter=filter,
                limit=10000,
                output_fields=output_fields,
        )
        return docs

if __name__ == "__main__":
    # 获得连接
    milvus_util = MilvusUtil()

    # 创建与删除知识库
    # print(milvus_util.drop_collection("py_test"))
    # print(milvus_util.drop_collection("jiu_ti"))

    # 文档是否已经存在
    # params = "source == '{}'".format("体质问答6.28.docx")
    # print(milvus_util.document_is_exists("jiu_ti", params))

    # # 删除文档
    # params = "source == '{}'".format("1.json")
    # milvus_util.del_document("jiu_ti", params)

