#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :text_embed_util.py
@Description  :文本嵌入工具
<AUTHOR>
@Date         :2024/12/3 17:04:31
'''


from configs.model_config import ModelConfig
import openai

class TextEmbedService(object):
    """
    文本嵌入服务
    """

    def __init__(self):
        # 嵌入模型客户端
        self.embed_model_client = EmbedModel.get_model_client()

    def text_embedding(self, sentence_list):
        """
        本文嵌入
        :param sentence_list: 句子列表
        :return:
        """
        # 解析结果
        embed_list = []
        # 进行嵌入
        embed_res = self.embed_model_client.embeddings.create(model=ModelConfig.EMBED_MODEL_NAME, input=sentence_list)
        for embed_item in embed_res.data:
            embed_list.append(embed_item.embedding)
        return embed_list


class EmbedModel(object):
    """
    向量模型
    """

    @staticmethod
    def get_model_client():
        """
        获取模型客户端
        :return:
        """
        # 返回模型客户端
        return openai.Client(api_key=ModelConfig.EMBED_API_KEY, base_url=ModelConfig.EMBED_API_BASE)