from datetime import datetime
from sqlalchemy import BigInteger, Column, Date, String, Text, Integer, DateTime
from sqlalchemy.ext.declarative import declarative_base
from pydantic import BaseModel, Field
from typing import List, Optional
from enum import Enum
from service_data_manage.entity.data_manage_entity import Document_DataType


Base = declarative_base()

class Task(Base):
    """任务基本信息表"""
    __tablename__ = 'Tasks'
    __table_args__ = {'comment': '存储任务基本信息及关联的产业链位置信息'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='任务唯一标识')
    TaskName = Column(String(50), nullable=False, comment='任务名称')
    TaskCategory = Column(String(50), nullable=False, comment='任务类别')
    Create_time = Column(DateTime, default=datetime.now, comment='任务开始时间')
    MongoCollection = Column(String(50), nullable=True, comment='关联的MongoDB集合名称')
    MongoID = Column(String(50), nullable=True, comment='关联的MongoDB文档ID')


class Product(Base):
    """产品词信息表"""
    __tablename__ = 'Products'
    __table_args__ = {'comment': '存储抽取的产品词信息'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='产品词唯一标识')
    ProductName = Column(String(50), nullable=False, comment='产品词名称')
    ProductSource = Column(String(20), nullable=False, comment='来源类型：专利或年报')
    Source_info = Column(String(50), nullable=True, comment='专利ID（如果是专利来源）')
    Textblock = Column(Text, nullable=True, comment='关联的原始文本内容')
    Reason_process = Column(Text, nullable=True, comment='从文本中提取产品词的推理过程')
    Industry_last_level = Column(String(20), nullable=True, comment='产品所属产业链末级环节')
    Task_id = Column(Integer, nullable=False, comment='关联的任务ID')
    Version = Column(Integer, nullable=False, comment='版本号')
    Create_time = Column(DateTime, default=datetime.now, comment='记录创建时间')


class ProductUnique(Base):
    """唯一产品词表"""
    __tablename__ = 'Products_Unique'
    __table_args__ = {'comment': '存储单独的产品词信息'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='产品词唯一标识')
    ProductName = Column(String(50), nullable=False, comment='产品词名称')
    ProductSource = Column(String(20), nullable=False, comment='来源类型：产品词或末级环节')
    Task_id = Column(Integer, nullable=False, comment='关联的任务ID')

  

class BasicTag(Base):
    """标准词表"""
    __tablename__ = 'Basic_Tags'
    __table_args__ = {'comment': '存储标准词信息'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='标准词唯一标识')
    TagsName = Column(String(50), nullable=False, comment='标准词名称')


class PatentInfo(Base):
    __tablename__ = 'PatentInfo'
   
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='自增序列')
    PatentId = Column(String(50), primary_key=True, nullable=False, comment='专利唯一标识符')
    PatentTitle = Column(String(1000), nullable=False, comment='专利名称')
    PatentType = Column(String(200), comment='专利类型')
    CountryOfRegistration = Column(String(600), comment='注册地区')
    LegalStatus = Column(Text, comment='法律状态（含历史记录）')
    LegalStatusdate = Column(DateTime, comment='法律状态日期')
    CurrentLegalStatus = Column(String(600), comment='简单法律状态')
    CurrentLegalStatusdate = Column(Date, comment='简单状态日期')
    FilingDate = Column(DateTime, comment='申请日')
    ApplicationNumber = Column(String(100), comment='申请号')
    GrantPublicationNumber = Column(String(100), comment='授权公告号')
    GrantPublicationDate = Column(DateTime, comment='授权公告日')
    PriorityNumber = Column(String(50), comment='优先权号')
    PriorityDate = Column(Date, comment='优先权日')
    IPCClass = Column(Text, comment='IPC分类号')
    GBCClass = Column(String(50), comment='GBC分类号')
    CPCClass = Column(String(50), comment='CPC分类号')
    ApplicantAndPatentee = Column(Text, comment='专利权人')
    PreviousApplicants = Column(Text, comment='历史专利权人')
    Inventors = Column(Text, comment='发明人')
    Address = Column(String(1000), comment='地址')
    PostalCode = Column(String(100), comment='邮编')
    PatentAgency = Column(String(1000), comment='代理机构')
    PatentAgent = Column(String(1000), comment='代理人')
    ApplicationStage = Column(String(50), comment='申请进度')
    AbstractText = Column(Text, comment='摘要')
    DrawingsPath = Column(Text, comment='摘要附图')
    ClaimsText = Column(Text, comment='权利要求')
    SpecificationText = Column(Text, comment='说明书')
    OriginalDocument = Column(Text, comment='原文档链接')


class TaskPatentRelationship(Base):
    """
    任务-专利关联表 ORM 模型
    说明：存储任务与专利授权号的关联关系（多对多）
    """
    __tablename__ = 'Task_Patent_relationship'
   

    # 主键字段（复合主键）
    task_id = Column(Integer,primary_key=True,nullable=False,comment='任务ID')
    patent_GrantPublicationNumber = Column(String(100),primary_key=True,nullable=False,comment='专利授权公告号')
