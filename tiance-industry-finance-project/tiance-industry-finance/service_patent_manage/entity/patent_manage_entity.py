"""
# @Time         : 2025/07/15 10:20:00
# <AUTHOR> <PERSON><PERSON>
# @File         : patent_manage_entity.py
# @Description  : 数据管理实体类
"""
from fastapi import UploadFile, File, Body
from pydantic import BaseModel, Field
from typing import List, Optional, Literal
from datetime import datetime
from typing import Optional
from enum import Enum


class TaskCreateRequest(BaseModel):
    """公司数据修改请求实体"""
    task_name: str = Field(..., example="广东低空经济-广东建行POC", description="任务名称")
    task_category: str = Field(..., example="低空经济", description="任务类别")
    monogid: str = Field(None, example="77b51a6065b44cfbb9168a57a7923f34", description="对应的产业链id（可选）")

class ProductExtractRequest(BaseModel):
    """产品抽取请求实体"""
    task_id: str = Field(..., example="1", description="任务id")

class ImportPatentRequest(BaseModel):
    """专利导入请求实体"""
    file: UploadFile = File(..., description="文件")
    task_id: str = Field(..., example="1", description="任务id")
    
    