#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/07/15 9:15:00
# <AUTHOR> <PERSON><PERSON>
# @File         : patent_manage_route.py
# @Description  : 数据管理API路由
"""
import io
import re
import os
import aiofiles
import asyncio
import pandas as pd
from fastapi import APIRouter, UploadFile, Body, File, Form
from fastapi.responses import StreamingResponse
from fastapi import APIRouter, HTTPException, Body, Query, Response
from typing import Union, List, Optional
from fastapi import APIRouter, Depends, HTTPException
from configs.patent_manage_config import PatentManageConfig
from service_patent_manage.entity.patent_manage_entity import ImportPatentRequest, ProductExtractRequest, TaskCreateRequest
from service_patent_manage.model.patent_model import PatentInfo, Task
from service_patent_manage.service.patent_manage_service import PatentManageService
from utils.log_util import LogUtil
from utils.mongodb_util import MongodbUtil
from utils.ret_util import RetUtil
from datetime import datetime

from utils.sql_util import SQLUtil

router = APIRouter()

@router.post("/create_task", summary="新建任务")
async def create_task(request: TaskCreateRequest):
    """
    新建任务接口

    功能说明：
    1. 创建一个新的任务
    2. 将任务信息保存到数据库中

    Args:
        request: 任务创建请求参数
            - task_name (str): 任务名称
            - task_category (str): 任务类别
            - monogid (str): 产业链环节id

    Returns:
        SuccessResponse: 包含任务ID的响应
        
    """
    try:
        # 记录请求日志
        LogUtil.log_json(describe="新建任务请求", kwargs=dict(request))
        # 调用服务层创建任务
        if request.monogid ==None:
            mongoid = ""
        else:
            mongoid = request.monogid
        data = {
            "TaskName": request.task_name,
            "TaskCategory": request.task_category,
            "Create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "MongoCollection": PatentManageConfig.MONGODB_COLLECTION,
            "MongoID": mongoid
        }
        task_info = PatentManageService.create_data(Task)
        return RetUtil.response_ok(data=task_info)

        # 记录返回日志
    except Exception as e:
        detail = f"服务器错误:{e}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        return RetUtil.response_error(data=detail)



@router.post("/product_extract", summary="从专利数据中抽取产品")
async def product_extract(request: ProductExtractRequest):
    """
    抽取产品词
    Args:
        request: 任务id

    Returns:
        SuccessResponse: 包含统计数据的响应
    """
    try:
        db = SQLUtil.get_session()
        # 记录请求日志
        LogUtil.log_json(describe="从专利数据中抽取产品", kwargs=dict(request))

        ## 获取任务信息
        task_info = PatentManageService.get_data_info(Task,request.id)

        ## 获取专利数据
        patent_data = PatentManageService.get_patent_data(request.id)

        ## 获取产业链信息
        collection = task_info["MongoCollection"]
        mongoid = task_info["MongoID"]
        if mongoid == "":
            return RetUtil.response_ok(data="产业链环节还未生成")
        chain_info = MongodbUtil.query_doc_by_id(collection, mongoid)["chain_structure"]
        industry_info = list(chain_info.keys())
        for industry in industry_info:
            ## 获取末级环节
            last_level =  industry.split('|')[-1]
            keywords_list = PatentManageService.cut_words(last_level)
            res = []

            ## 筛选对应的专利数据
            for row in patent_data:
                search_content = row["PatentTitle"]+row["AbstractText"][:30]
                pattern = PatentManageService.build_pattern(keywords_list)
                regex = re.compile(pattern)
                has_keyword = bool(regex.search(search_content))
                if has_keyword:
                    res.append({"专利号": row["GrantPublicationNumber"], "专利名称": row["PatentTitle"], "专利摘要": row["AbstractText"]})
            ## 执行抽取过程
            save_path  =  PatentManageConfig.EXTRACT_SAVE_PATH + "last_level"
            PatentManageService.extract_product(last_level, res, save_path)

        ## 大模型抽取完后，处理抽取结果数据
        json_format = task_info["task_name"] + "*.json"
        output_path = task_info["task_name"] + "产业链抽取产品结果.json"
        PatentManageService.process_json_data(json_format,output_path)

        ## 获取当前抽取的产品词是第几个版本
        res = PatentManageService.get_max_version(db,request.id)
        if res == None:
            version = 1
        else:
            version = res + 1
        ## 将抽取到的产品信息保存到数据库中
        PatentManageService.process_product_to_save_mysql(output_path,task_info["TaskCategory"],request.id,version)

        ## 对产品词按照规则删除：
        # 同一产业链类型下该产品词=产业链环节词（不限层级）时，且产品词出现数量>=500，且不同末级环节>=3个包含该产品词，该产品词需全删除掉
        PatentManageService.remove_duplicate_products(db,request.id,version)

        return RetUtil.response_ok(data={"产品词抽取成功，已入库"})

    except Exception as e:
        error_detail = f"产品词抽取失败{str(e)}"
        LogUtil.error(msg=error_detail)

        error_data = {
            "error": error_detail,
            "error_type": "company_data_stats_error"
        }
        return RetUtil.response_error(data=error_data)


@router.post("/import_patent_data", summary="导入")
async def product_extract(request: ImportPatentRequest):

    if not request.file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(400, "仅支持 .xlsx 或 .xls 文件")
    try:
        # 记录请求日志
        LogUtil.log_json(describe="导入专利数据", kwargs=dict(request))
        contents = await request.file.read()
        PatentManageService.save_patent(contents)

        # 记录返回日志

    except Exception as e:
        return RetUtil.response_error()


# @router.post("/match_product_tag", summary="匹配标准词和产品词")
# async def product_extract(request: MatchProductTagRequest):

#     """
#     匹配标准词和产品词，将产品词匹配上的标准词id存入产品词表中
#     Args:
#         ProductExtractRequest: 
#               - task_id (int): 任务id
#               - version (int): 版本id

#     Returns:
#         SuccessResponse: 包含入库信息的响应
#     """
#     ## 获取产品词

#     ## 获取标准词
