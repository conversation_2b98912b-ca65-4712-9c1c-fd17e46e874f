#!/usr/bin/env python
# -*- encoding: utf-8 -*-


from configs.api_config import ApiConfig
from fastapi import APIRouter

from service_patent_manage.api.router import patent_manage_route


api_router = APIRouter()

api_router.include_router(patent_manage_route.router, prefix=ApiConfig.DATA_MANAGE_ROUTE, tags=["产品抽取"])


# def get_db(request: Request):
#     db = request.app.state.SessionLocal()
#     try:
#         yield db
#     finally:
#         db.close()
