from collections import OrderedDict, defaultdict
from copy import deepcopy
from datetime import datetime
import glob
import io
import json
import re
import jieba
import pandas as pd
from sqlalchemy import func
from sqlalchemy.orm import Session
from typing import Dict
from configs.patent_manage_config import PatentManageConfig
from service import llm_service
from service_patent_manage.model.patent_model import PatentInfo, Product, Task, TaskPatentRelationship
from utils.llm_model_util import Llm_Service, MessageConverter, UserMessage
from utils.log_util import LogUtil
from utils.sql_util import SQLUtil
from utils.text_utils import TextUtil


class PatentManageService:
    """专利数据管理服务类"""

    @staticmethod
    def create_data(model,data):
        """
        新增数据
        :param model:数据库表对应的Orm模型
                data:插入的数据（字典类型）
        :return: 新增的记录
        """
        try:
            res = SQLUtil.insert_one(model,data)
            return res
        except Exception as e:
            LogUtil.error(f"{str(e)}")
            raise Exception(e)
        
    @staticmethod
    def get_data_info(model,id):
        """
        根据id获取数据
        :param model:数据库表对应的Orm模型
                id:主键
        :return: 检索的记录
        """
        try:
            res = SQLUtil.get_data_by_id(model,id)
            return SQLUtil.model_to_dict(res)
        except Exception as e:
            LogUtil.error(f"{str(e)}")
            raise Exception(e)
        
    @staticmethod
    def get_patent_data(db: Session, task_id: int):
        """
        根据任务id获取专利
        :param model:数据库表对应的Orm模型
                id:主键
        :return: 检索的记录
        """
        try:
            results = db.query(
                PatentInfo.PatentTitle,
                PatentInfo.GrantPublicationNumber,
                PatentInfo.AbstractText
            ).join(
                TaskPatentRelationship,
                PatentInfo.GrantPublicationNumber == TaskPatentRelationship.patent_GrantPublicationNumber
            ).filter(
                TaskPatentRelationship.task_id == task_id
            ).all()
            return SQLUtil.models_to_list(results)
        except Exception as e:
            LogUtil.error(f"{str(e)}")
            raise Exception(e)

    @staticmethod
    def extract_product(industry: str, patent_data: Dict, filename:str):
        """
        提取产品
        :param industry: 行业
        :param patent_data: 专利数据
        :filename: 抽取结果存储位置
        :return: 产品列表
        """
        
        size = PatentManageConfig.EXTRACTSIZE
        total_size = len(patent_data)

        for i in range(0, total_size, size):
            industry_dict = defaultdict(list)
            data_piece = patent_data[i:i+size]
            
        # data=data[:20]
        # # print(len(data))
            content = []
            for row in data_piece:
                patentInfo_json = {
                    '专利名称': row['专利名称'],
                    '专利号': row['专利号'],
                    '专利摘要': row['专利摘要'],
                }
                content.append(patentInfo_json)

            try:
                res = PatentManageService.extract_product_llm_from_patent(industry, content)
            except Exception as e:
                print(f"分块 {i}-{i+size} 抽取专利信息时出错: {e}")
                continue

            for patent in res:
                try:
                    patent_name = patent['专利名称']
                    patent_code = patent['专利号']
                    for extraction in patent['抽取结果']:
                        keywords = extraction['关键词']
                        text_block = extraction['文本块']
                        process = extraction['推理过程']
                        
                        
                        patent_info = {
                            '关键词': keywords,
                            '专利号': patent_code,
                            '专利名称': patent_name,
                            '文本块': text_block,
                            '推理过程': process,
                        }
                                # 添加到对应产业链字典
                        industry_dict[industry].append(patent_info)
                except KeyError as e:
                    print(f"专利 {patent.get('专利名称','无名专利')} 缺少必要字段: {e}")
                    continue
            # print(industry_dict)
            output_path = filename + str(i) + ".json"
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(industry_dict, f, ensure_ascii=False, indent=2)
            print(f"Processed {i+size} out of {total_size} records")


    @staticmethod
    def extract_product_llm_from_patent(industry, content):
        """
        提取产品
        :param industry: 产业链末级环节
        :param content:    专利数据
        :return: 抽取结果本地存json文件
        """
        llm_service = Llm_Service()

        prompt = PatentManageConfig.EXTRACT_DIKONG_PROMPT.format(industry=industry,content =content)
        answer = llm_service.answer_question(MessageConverter.convert_messages([UserMessage(prompt)]),
                                                PatentManageConfig.EXTRACT_PRODUCT_MODEL)
        
        answer = TextUtil.remove_think(answer)
        prettify_json = PatentManageService.process_result(answer)
        return prettify_json
    
    
    @staticmethod
    def process_result(content):
        pattern = r'\[(.*)\]'
        match = re.search(pattern, content, re.DOTALL)
        if match:
            # 提取 JSON 字符串
            json_str = '[' + match.group(1) + ']'
            
            # 解析 JSON
            json_str = PatentManageService.fix_trailing_comma(json_str)
            # print(json_str)
            llm_data = json.loads(json_str)
        else:
            llm_data = []
    
        return llm_data
    
    @staticmethod
    def fix_trailing_comma(json_str):
        """自动移除对象/数组末尾的逗号"""
        # 修正对象末尾的逗号: { "key": "value", } → { "key": "value" }
        json_str = re.sub(r',\s*}(?=\s*[}\]])', '}', json_str)
        # 修正数组末尾的逗号: [1, 2, ] → [1, 2]
        json_str = re.sub(r',\s*\](?=\s*[}\]])', ']', json_str)
        return json_str
    
    @staticmethod
    def process_json_data(json_format,output_json):
        """
        处理原始json数据，整合成最终可用的json数据
        josn_format  需要合并的json格式
        """

        ## 去重
        json_files = glob.glob('*.json') 
        for file in json_files:
            PatentManageService.remove_duplicates(file)
            print(file+"去重完成")
        
        ## 合并
        PatentManageService.merge_json_files(json_format)  ## "*.json"

        ## 处理标点符号
        PatentManageService.process_json_file('merged.json',"final.json")

        ## 分割关键词
        PatentManageService.split_keywords_in_json("final.json",output_json)

    @staticmethod
    def remove_duplicates(file_name):
        with open(file_name, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        cleaned_data = {}
        for industry, patents in original_data.items():
            # 使用字典去重，以"专利名称"+"文本块"作为唯一标识
            unique_patents = {f"{p['专利名称']}|{p['专利号']}": p for p in patents}
            cleaned_data[industry] = list(unique_patents.values())

        with open(file_name, 'w', encoding='utf-8') as f:
            json.dump(cleaned_data, f, ensure_ascii=False, indent=2)
        return cleaned_data
    
    @staticmethod
    def merge_json_files(json_format):
        # 1. 获取所有JSON文件
        json_files = glob.glob(json_format)  # 或指定路径如 'data/*.json'
        # json_files = ["file10.json","file110.json","file130.json"]

        # 2. 创建合并字典
        merged_data = defaultdict(list)
        # 3. 遍历并合并文件
        for file in json_files:
            with open(file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                for industry, patents in data.items():
                    merged_data[industry].extend(patents)  # 合并同名键
        # 4. 保存结果
        with open('merged_news.json', 'w', encoding='utf-8') as f:
            json.dump(dict(merged_data), f, ensure_ascii=False, indent=2)
        print(f"已合并 {len(json_files)} 个文件，包含 {len(merged_data)} 个产业链")


    @staticmethod
    def process_json_file(input_file, output_file):
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        for industry, patents in data.items():
            for patent in patents:
                patent['文本块'] = TextUtil.remove_punctuation(patent['文本块'])
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"已处理 {input_file}，结果保存至 {output_file}")

    @staticmethod
    def split_keywords_in_json(input_file, output_file):
        """
        处理JSON文件中的关键词分割 
        :param input_file: 输入JSON文件路径
        :param output_file: 输出JSON文件路径
        """
        # 1. 读取原始JSON文件
        with open(input_file, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        # 2. 处理数据（分割关键词）
        processed_data = {}
        for category, patents in original_data.items():
            expanded_patents = []
            for patent in patents:
                # 分割关键词并清理空白字符
                keywords = [kw.strip() for kw in patent["关键词"].split(",") if kw.strip()]
                
                # 为每个关键词创建新条目
                for keyword in keywords:
                    new_entry = deepcopy(patent)
                    new_entry["关键词"] = keyword
                    expanded_patents.append(new_entry)
            
            processed_data[category] = expanded_patents
        
        # 3. 写入处理后的JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_data, f, indent=2, ensure_ascii=False)
        
        print(f"处理完成！结果已保存到: {output_file}")

    @staticmethod
    def get_company_data_statistics():
        """
        获取公司数据统计信息
        :return: 包含总量、上市公司总数、当日更新总数的字典
        """
        pass

    @staticmethod
    def cut_words(text):
        words = jieba.lcut(text)
        words = PatentManageService.clean_word_list(words)
        return words
    
    @staticmethod
    def clean_word_list(word_list):
        # 1. 去除空格和空字符串
        cleaned = [word.strip() for word in word_list if word.strip()]
        
        # 2. 去除标点符号（中文和英文标点）
        cleaned = [re.sub(r'[^\w\s]', '', word) for word in cleaned]
        
        # 3. 再次去除可能因去除标点产生的空字符串
        cleaned = [word for word in cleaned if word]
        
        # 4. 去除重复词并保持原始顺序
        cleaned = list(OrderedDict.fromkeys(cleaned))
    
        return cleaned
    
    @staticmethod
    def build_pattern(items):
        patterns = ""
        for item in items:
            pattern = f'(?=.*{item})'
            patterns += pattern
        return patterns
    
    @staticmethod
    def process_product_to_save_mysql(json_name,total_industry,taskid,version):
        ## 将产品信息处理成mysql可插入的格式
    
        ## 将产品信息json文件清理干净
        with open(json_name, 'r', encoding='utf-8') as f:
            industry_dict = json.load(f)
        
        products_data = []
        for tech_category, patents in industry_dict.items():
            for patent in patents:
                keywords = patent.get('关键词', '')
                text = patent.get('文本块', '')
                # if patent.get('关键词', '') in filtered_list:
                #         continue
                ## 验证产品词是否在文本块中
                if PatentManageService.vaild_keywords_in_block(keywords,text) == False:
                    continue
                ## 将产品词中带有标点符号的删掉
                if len(PatentManageService.clean_keyword_regex(keywords)) == 1:
                    continue

                ## 去除 产品词和产业链名称相同的
                if keywords == total_industry.strip():
                    continue

                ## 去除产品词中带有第几等关键词的
                pattern = r'^(第[一二三四五六七八九十\d]+|No\.\d+)'
                if bool(re.match(pattern, keywords)):
                    continue
                data = {
                    "ProductName": keywords,
                    "ProductSource": '专利',
                    "Source_info":  patent.get('专利号', ''),
                    "Textblock": text,
                    "Reason_process": patent.get('推理过程', ''),
                    "Indusrty_last_level": tech_category,
                    "Task_id": taskid,
                    "Version": version,
                    "Create_time": datetime.now()

                }
                products_data.append(data)
        SQLUtil.insert_many(Product,products_data)

    @staticmethod
    def vaild_keywords_in_block(keyword,text):
        if len(keyword) <2:
            return keyword in text
        else:
            keywords_list = PatentManageService.cut_words(keyword)
            pattern = PatentManageService.build_pattern(keywords_list) 
            regex = re.compile(pattern)
            return bool(regex.search(text))
        
    @staticmethod
    def clean_keyword_regex(keyword):
        # 匹配所有标点和数学符号（包括全角/半角）
        pattern = r'[\s+\-\*/×÷=±≈≠≤≥<>，。、；：‘’“”【】《》！？…—（）·]'
        return re.sub(pattern, '', keyword)
    
    @staticmethod
    def get_max_version(db:Session,task_id:int):
        return  db.query(func.max(Product.Version)).filter(Product.Task_id == task_id).scalar()
    

    @staticmethod
    def save_patent(contents,task_id):
        """
        将专利数据保存到专利表的同时，将任务和专利数据的对应关系保存
        :param contents: 文件内容
        
        """

         # 使用 pandas 读取 Excel（BytesIO 包装二进制数据）
        df = pd.read_excel(io.BytesIO(contents), engine='openpyxl')  # engine 可选 'openpyxl' 或 'xlrd'
        patents_data = []
        relationships_data =[]
        # 转换为字典列表（或其他格式）
        for row in df.iterrows():
            patentTitle = str(row['标题'])
            if patentTitle == 'nan':
                patentTitle = None

            PatentType = str(row['专利类型'])
            if PatentType == 'nan':
                PatentType = None

            CountryOfRegistration = "ZN"

            LegalStatus = str(row['法律状态'])
            if LegalStatus == 'nan':
                LegalStatus = None

            CurrentLegalStatus = str(row['当前法律状态'])
            if CurrentLegalStatus == 'nan':
                CurrentLegalStatus = None

            FilingDate = str(row['申请日'])
            if FilingDate == 'nan':
                FilingDate = None


            GrantPublicationNumber = str(row['申请号'])
            if GrantPublicationNumber == 'nan':
                GrantPublicationNumber = None
                ApplicationNumber = None
            else:
                ApplicationNumber = GrantPublicationNumber[2:]

            IPCClass = str(row['IPC'])
            if IPCClass == 'nan':
                IPCClass = None

            ApplicantAndPatentee = str(row['专利权人'])
            if ApplicantAndPatentee == 'nan':
                ApplicantAndPatentee = None

            Inventors = str(row['发明人'])
            if Inventors == 'nan':
                Inventors = None

            Address = str(row['申请人地址'])
            if Address == 'nan':
                Address = None

            PatentAgency = str(row['代理机构'])
            if PatentAgency == 'nan':
                PatentAgency = None

            PatentAgent = str(row['代理人'])
            if PatentAgent == 'nan':
                PatentAgent = None

            AbstractText = str(row['摘要'])
            if AbstractText == 'nan':
                AbstractText = None

            patent_data = {
                "PatentTitle": patentTitle,
                "PatentType": PatentType,
                "CountryOfRegistration": CountryOfRegistration,
                "LegalStatus": LegalStatus,
                "CurrentLegalStatus": CurrentLegalStatus,
                "FilingDate": FilingDate,
                "GrantPublicationNumber": GrantPublicationNumber,
                "ApplicationNumber": ApplicationNumber,
                "IPCClass": IPCClass,
                "ApplicantAndPatentee": ApplicantAndPatentee,
                "Inventors": Inventors,
                "Address": Address,
                "PatentAgency": PatentAgency,
                "PatentAgent": PatentAgent,
                "AbstractText": AbstractText
            }
            relationship_data ={
                "task_id": task_id,
                "patent_GrantPublicationNumber": GrantPublicationNumber,
            }

            patents_data.append(patent_data)
            relationships_data.append(relationship_data)

        SQLUtil.insert_many(PatentInfo, patents_data)
        SQLUtil.insert_many(TaskPatentRelationship, relationships_data)

    @staticmethod
    def remove_duplicate_products(db:Session,task_id:int,version:int):
        # 删除产品词超过500个的产品
        subquery = (
            db.query(Product.ProductName)
            .filter(Product.Task_id == task_id, Product.Version == version)
            .group_by(Product.Industry_last_level, Product.ProductName)
            .having(func.count(Product.ProductName) > 500)
        ).subquery()
        # 执行删除
        delete_count = (
            db.query(Product)
            .filter(Product.ProductName.in_(subquery))
            .delete(synchronize_session=False)
        )

        # 删除在不同环节多次出现的产品
        results = (
            db.query(Product.ProductName)
            .filter(Product.Task_id == task_id, Product.Version == version)
            .group_by(Product.ProductName)
            .having(func.count(func.distinct(Product.Industry_last_level)) >= 3)
            .all()
        )

        delete_count2 = (
            db.query(Product)
            .filter(Product.ProductName.in_(results))
            .delete(synchronize_session=False)
        )

        
        db.commit()